(['C:\\Users\\<USER>\\Desktop\\7.29\\my_script.py'],
 ['C:\\Users\\<USER>\\Desktop\\7.29'],
 ['tkinter',
  'tkinter.ttk',
  'tkinter.messagebox',
  'tkinter.filedialog',
  'pandas',
  'openpyxl',
  'PyPDF2',
  'pdfplumber',
  'PIL',
  'PIL.Image',
  'pyperclip'],
 [('C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 ['matplotlib',
  'numpy.tests',
  'pandas.tests',
  'unittest',
  'test',
  'torch',
  'tensorflow',
  'cv2',
  'scipy',
  'sklearn',
  '__main__'],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.12.5 (tags/v3.12.5:ff3bc82, Aug  6 2024, 20:45:27) [MSC v.1940 64 bit '
 '(AMD64)]',
 [('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('my_script',
   'C:\\Users\\<USER>\\Desktop\\7.29\\my_script.py',
   'PYSOURCE')],
 [('zipfile', 'D:\\aaaaa\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path', 'D:\\aaaaa\\Lib\\zipfile\\_path\\__init__.py', 'PYMODULE'),
  ('zipfile._path.glob', 'D:\\aaaaa\\Lib\\zipfile\\_path\\glob.py', 'PYMODULE'),
  ('contextlib', 'D:\\aaaaa\\Lib\\contextlib.py', 'PYMODULE'),
  ('argparse', 'D:\\aaaaa\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\aaaaa\\Lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'D:\\aaaaa\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\aaaaa\\Lib\\gettext.py', 'PYMODULE'),
  ('py_compile', 'D:\\aaaaa\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\aaaaa\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib', 'D:\\aaaaa\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\aaaaa\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\aaaaa\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing', 'D:\\aaaaa\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.abc', 'D:\\aaaaa\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\aaaaa\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\aaaaa\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\aaaaa\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\aaaaa\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\aaaaa\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'D:\\aaaaa\\Lib\\tempfile.py', 'PYMODULE'),
  ('random', 'D:\\aaaaa\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'D:\\aaaaa\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'D:\\aaaaa\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\aaaaa\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\aaaaa\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'D:\\aaaaa\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\aaaaa\\Lib\\numbers.py', 'PYMODULE'),
  ('bisect', 'D:\\aaaaa\\Lib\\bisect.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\aaaaa\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'D:\\aaaaa\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'D:\\aaaaa\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\aaaaa\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime', 'D:\\aaaaa\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('string', 'D:\\aaaaa\\Lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\aaaaa\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\aaaaa\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'D:\\aaaaa\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators', 'D:\\aaaaa\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'D:\\aaaaa\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\aaaaa\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\aaaaa\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\aaaaa\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset', 'D:\\aaaaa\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'D:\\aaaaa\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\aaaaa\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._policybase', 'D:\\aaaaa\\Lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.header', 'D:\\aaaaa\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'D:\\aaaaa\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'D:\\aaaaa\\Lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr', 'D:\\aaaaa\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('calendar', 'D:\\aaaaa\\Lib\\calendar.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\aaaaa\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('ipaddress', 'D:\\aaaaa\\Lib\\ipaddress.py', 'PYMODULE'),
  ('socket', 'D:\\aaaaa\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'D:\\aaaaa\\Lib\\selectors.py', 'PYMODULE'),
  ('quopri', 'D:\\aaaaa\\Lib\\quopri.py', 'PYMODULE'),
  ('email', 'D:\\aaaaa\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'D:\\aaaaa\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\aaaaa\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('csv', 'D:\\aaaaa\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers', 'D:\\aaaaa\\Lib\\importlib\\readers.py', 'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\aaaaa\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\aaaaa\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize', 'D:\\aaaaa\\Lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\aaaaa\\Lib\\token.py', 'PYMODULE'),
  ('lzma', 'D:\\aaaaa\\Lib\\lzma.py', 'PYMODULE'),
  ('_compression', 'D:\\aaaaa\\Lib\\_compression.py', 'PYMODULE'),
  ('bz2', 'D:\\aaaaa\\Lib\\bz2.py', 'PYMODULE'),
  ('struct', 'D:\\aaaaa\\Lib\\struct.py', 'PYMODULE'),
  ('importlib.util', 'D:\\aaaaa\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\aaaaa\\Lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\aaaaa\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\aaaaa\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'D:\\aaaaa\\Lib\\ast.py', 'PYMODULE'),
  ('subprocess', 'D:\\aaaaa\\Lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'D:\\aaaaa\\Lib\\signal.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\aaaaa\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\aaaaa\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\aaaaa\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\aaaaa\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\aaaaa\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\aaaaa\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client', 'D:\\aaaaa\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'D:\\aaaaa\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('gzip', 'D:\\aaaaa\\Lib\\gzip.py', 'PYMODULE'),
  ('xml.parsers.expat', 'D:\\aaaaa\\Lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.parsers', 'D:\\aaaaa\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml', 'D:\\aaaaa\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\aaaaa\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils', 'D:\\aaaaa\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('urllib.request', 'D:\\aaaaa\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('fnmatch', 'D:\\aaaaa\\Lib\\fnmatch.py', 'PYMODULE'),
  ('getpass', 'D:\\aaaaa\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'D:\\aaaaa\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'D:\\aaaaa\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\aaaaa\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes', 'D:\\aaaaa\\Lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\aaaaa\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http', 'D:\\aaaaa\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'D:\\aaaaa\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response', 'D:\\aaaaa\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib.error', 'D:\\aaaaa\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('xml.sax', 'D:\\aaaaa\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler', 'D:\\aaaaa\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\aaaaa\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader', 'D:\\aaaaa\\Lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('http.client', 'D:\\aaaaa\\Lib\\http\\client.py', 'PYMODULE'),
  ('hmac', 'D:\\aaaaa\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.context',
   'D:\\aaaaa\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\aaaaa\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\aaaaa\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\aaaaa\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\aaaaa\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\aaaaa\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\aaaaa\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes', 'D:\\aaaaa\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\aaaaa\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\aaaaa\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\aaaaa\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\aaaaa\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'D:\\aaaaa\\Lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\aaaaa\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\aaaaa\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\aaaaa\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\aaaaa\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\aaaaa\\Lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\aaaaa\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('pickle', 'D:\\aaaaa\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\aaaaa\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'D:\\aaaaa\\Lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\aaaaa\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('multiprocessing.process',
   'D:\\aaaaa\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'D:\\aaaaa\\Lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'D:\\aaaaa\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\aaaaa\\Lib\\zipimport.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\aaaaa\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('sysconfig', 'D:\\aaaaa\\Lib\\sysconfig.py', 'PYMODULE'),
  ('_aix_support', 'D:\\aaaaa\\Lib\\_aix_support.py', 'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('tarfile', 'D:\\aaaaa\\Lib\\tarfile.py', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('platform', 'D:\\aaaaa\\Lib\\platform.py', 'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\aaaaa\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\aaaaa\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\aaaaa\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\aaaaa\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent', 'D:\\aaaaa\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('site', 'D:\\aaaaa\\Lib\\site.py', 'PYMODULE'),
  ('rlcompleter', 'D:\\aaaaa\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('_sitebuiltins', 'D:\\aaaaa\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('pydoc', 'D:\\aaaaa\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'D:\\aaaaa\\Lib\\webbrowser.py', 'PYMODULE'),
  ('shlex', 'D:\\aaaaa\\Lib\\shlex.py', 'PYMODULE'),
  ('http.server', 'D:\\aaaaa\\Lib\\http\\server.py', 'PYMODULE'),
  ('socketserver', 'D:\\aaaaa\\Lib\\socketserver.py', 'PYMODULE'),
  ('html', 'D:\\aaaaa\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\aaaaa\\Lib\\html\\entities.py', 'PYMODULE'),
  ('pydoc_data.topics', 'D:\\aaaaa\\Lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('pydoc_data', 'D:\\aaaaa\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('tty', 'D:\\aaaaa\\Lib\\tty.py', 'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('configparser', 'D:\\aaaaa\\Lib\\configparser.py', 'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomllib', 'D:\\aaaaa\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'D:\\aaaaa\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._types', 'D:\\aaaaa\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tomllib._re', 'D:\\aaaaa\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('glob', 'D:\\aaaaa\\Lib\\glob.py', 'PYMODULE'),
  ('setuptools._shutil',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('ctypes.wintypes', 'D:\\aaaaa\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('__future__', 'D:\\aaaaa\\Lib\\__future__.py', 'PYMODULE'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys', 'D:\\aaaaa\\Lib\\colorsys.py', 'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('fileinput', 'D:\\aaaaa\\Lib\\fileinput.py', 'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('doctest', 'D:\\aaaaa\\Lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'D:\\aaaaa\\Lib\\pdb.py', 'PYMODULE'),
  ('code', 'D:\\aaaaa\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\aaaaa\\Lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'D:\\aaaaa\\Lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'D:\\aaaaa\\Lib\\cmd.py', 'PYMODULE'),
  ('difflib', 'D:\\aaaaa\\Lib\\difflib.py', 'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\ctypeslib\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib._ctypeslib',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\ctypeslib\\_ctypeslib.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._format_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_format_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\aaaaa\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\aaaaa\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\aaaaa\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\aaaaa\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree', 'D:\\aaaaa\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('PIL._util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('openpyxl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('stringprep', 'D:\\aaaaa\\Lib\\stringprep.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\aaaaa\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('_py_abc', 'D:\\aaaaa\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_strptime', 'D:\\aaaaa\\Lib\\_strptime.py', 'PYMODULE'),
  ('pdfminer.pdfparser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\pdfparser.py',
   'PYMODULE'),
  ('pdfminer.psparser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\psparser.py',
   'PYMODULE'),
  ('pdfminer.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\utils.py',
   'PYMODULE'),
  ('pdfminer.psexceptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\psexceptions.py',
   'PYMODULE'),
  ('pdfminer.pdftypes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\pdftypes.py',
   'PYMODULE'),
  ('pdfminer.runlength',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\runlength.py',
   'PYMODULE'),
  ('pdfminer.lzw',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\lzw.py',
   'PYMODULE'),
  ('pdfminer.ccitt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\ccitt.py',
   'PYMODULE'),
  ('pdfminer.ascii85',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\ascii85.py',
   'PYMODULE'),
  ('pdfminer.pdfexceptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\pdfexceptions.py',
   'PYMODULE'),
  ('pdfminer.casting',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\casting.py',
   'PYMODULE'),
  ('pdfminer.settings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\settings.py',
   'PYMODULE'),
  ('pdfminer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\__init__.py',
   'PYMODULE'),
  ('pdfminer.pdfpage',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\pdfpage.py',
   'PYMODULE'),
  ('pdfminer.pdfinterp',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\pdfinterp.py',
   'PYMODULE'),
  ('pdfminer.pdffont',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\pdffont.py',
   'PYMODULE'),
  ('pdfminer.fontmetrics',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\fontmetrics.py',
   'PYMODULE'),
  ('pdfminer.encodingdb',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\encodingdb.py',
   'PYMODULE'),
  ('pdfminer.latin_enc',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\latin_enc.py',
   'PYMODULE'),
  ('pdfminer.glyphlist',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\glyphlist.py',
   'PYMODULE'),
  ('pdfminer.pdfdevice',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\pdfdevice.py',
   'PYMODULE'),
  ('pdfminer.pdfcolor',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\pdfcolor.py',
   'PYMODULE'),
  ('pdfminer.cmapdb',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmapdb.py',
   'PYMODULE'),
  ('pdfminer.pdfdocument',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\pdfdocument.py',
   'PYMODULE'),
  ('pdfminer._saslprep',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\_saslprep.py',
   'PYMODULE'),
  ('pdfminer.data_structures',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\data_structures.py',
   'PYMODULE'),
  ('pdfminer.arcfour',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\arcfour.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('pdfminer.layout',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\layout.py',
   'PYMODULE'),
  ('pdfminer.converter',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\converter.py',
   'PYMODULE'),
  ('pdfminer.image',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\image.py',
   'PYMODULE'),
  ('pdfminer.jbig2',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\jbig2.py',
   'PYMODULE'),
  ('pyperclip',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('PyPDF2',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\__init__.py',
   'PYMODULE'),
  ('PyPDF2.papersizes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\papersizes.py',
   'PYMODULE'),
  ('PyPDF2.pagerange',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\pagerange.py',
   'PYMODULE'),
  ('PyPDF2.errors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\errors.py',
   'PYMODULE'),
  ('PyPDF2._writer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_writer.py',
   'PYMODULE'),
  ('PyPDF2.types',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\types.py',
   'PYMODULE'),
  ('PyPDF2.generic._outline',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\generic\\_outline.py',
   'PYMODULE'),
  ('PyPDF2.generic._data_structures',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\generic\\_data_structures.py',
   'PYMODULE'),
  ('PyPDF2.filters',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\filters.py',
   'PYMODULE'),
  ('PyPDF2.xmp',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\xmp.py',
   'PYMODULE'),
  ('xml.dom.minidom', 'D:\\aaaaa\\Lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'D:\\aaaaa\\Lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\aaaaa\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter', 'D:\\aaaaa\\Lib\\xml\\dom\\NodeFilter.py', 'PYMODULE'),
  ('xml.dom.xmlbuilder', 'D:\\aaaaa\\Lib\\xml\\dom\\xmlbuilder.py', 'PYMODULE'),
  ('xml.dom.minicompat', 'D:\\aaaaa\\Lib\\xml\\dom\\minicompat.py', 'PYMODULE'),
  ('xml.dom.domreg', 'D:\\aaaaa\\Lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom', 'D:\\aaaaa\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('PyPDF2.generic._utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\generic\\_utils.py',
   'PYMODULE'),
  ('PyPDF2._codecs',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_codecs\\__init__.py',
   'PYMODULE'),
  ('PyPDF2._codecs.zapfding',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_codecs\\zapfding.py',
   'PYMODULE'),
  ('PyPDF2._codecs.symbol',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_codecs\\symbol.py',
   'PYMODULE'),
  ('PyPDF2._codecs.std',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_codecs\\std.py',
   'PYMODULE'),
  ('PyPDF2._codecs.pdfdoc',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_codecs\\pdfdoc.py',
   'PYMODULE'),
  ('PyPDF2._codecs.adobe_glyphs',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_codecs\\adobe_glyphs.py',
   'PYMODULE'),
  ('PyPDF2.generic._fit',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\generic\\_fit.py',
   'PYMODULE'),
  ('PyPDF2._protocols',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_protocols.py',
   'PYMODULE'),
  ('PyPDF2.generic._base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\generic\\_base.py',
   'PYMODULE'),
  ('PyPDF2.generic',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\generic\\__init__.py',
   'PYMODULE'),
  ('PyPDF2.generic._rectangle',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\generic\\_rectangle.py',
   'PYMODULE'),
  ('PyPDF2.generic._annotations',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\generic\\_annotations.py',
   'PYMODULE'),
  ('PyPDF2.constants',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\constants.py',
   'PYMODULE'),
  ('PyPDF2._utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_utils.py',
   'PYMODULE'),
  ('PyPDF2._security',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_security.py',
   'PYMODULE'),
  ('PyPDF2._version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_version.py',
   'PYMODULE'),
  ('PyPDF2._reader',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_reader.py',
   'PYMODULE'),
  ('PyPDF2._page',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_page.py',
   'PYMODULE'),
  ('PyPDF2._cmap',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_cmap.py',
   'PYMODULE'),
  ('PyPDF2._merger',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_merger.py',
   'PYMODULE'),
  ('PyPDF2._encryption',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_encryption.py',
   'PYMODULE'),
  ('pdfplumber',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\__init__.py',
   'PYMODULE'),
  ('pdfplumber.repair',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\repair.py',
   'PYMODULE'),
  ('pdfplumber.pdf',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\pdf.py',
   'PYMODULE'),
  ('pdfplumber.utils.exceptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\utils\\exceptions.py',
   'PYMODULE'),
  ('pdfplumber.structure',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\structure.py',
   'PYMODULE'),
  ('pdfplumber.utils.geometry',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\utils\\geometry.py',
   'PYMODULE'),
  ('pdfplumber.utils.clustering',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\utils\\clustering.py',
   'PYMODULE'),
  ('pdfplumber.page',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\page.py',
   'PYMODULE'),
  ('pdfplumber.display',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\display.py',
   'PYMODULE'),
  ('pypdfium2',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\__init__.py',
   'PYMODULE'),
  ('pypdfium2.internal',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\internal\\__init__.py',
   'PYMODULE'),
  ('pypdfium2.internal.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\internal\\utils.py',
   'PYMODULE'),
  ('pypdfium2.internal.consts',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\internal\\consts.py',
   'PYMODULE'),
  ('pypdfium2.internal.bases',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\internal\\bases.py',
   'PYMODULE'),
  ('pypdfium2.raw',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\raw.py',
   'PYMODULE'),
  ('pypdfium2_raw.bindings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2_raw\\bindings.py',
   'PYMODULE'),
  ('pypdfium2_raw',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2_raw\\__init__.py',
   'PYMODULE'),
  ('ctypes.util', 'D:\\aaaaa\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\aaaaa\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\aaaaa\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\aaaaa\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\aaaaa\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\aaaaa\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('pypdfium2._helpers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_helpers\\__init__.py',
   'PYMODULE'),
  ('pypdfium2._helpers.textpage',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_helpers\\textpage.py',
   'PYMODULE'),
  ('pypdfium2._helpers.pageobjects',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_helpers\\pageobjects.py',
   'PYMODULE'),
  ('pypdfium2._helpers.page',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_helpers\\page.py',
   'PYMODULE'),
  ('pypdfium2._helpers.attachment',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_helpers\\attachment.py',
   'PYMODULE'),
  ('pypdfium2._helpers.document',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_helpers\\document.py',
   'PYMODULE'),
  ('pypdfium2._helpers.bitmap',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_helpers\\bitmap.py',
   'PYMODULE'),
  ('pypdfium2._helpers.matrix',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_helpers\\matrix.py',
   'PYMODULE'),
  ('pypdfium2._helpers.misc',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_helpers\\misc.py',
   'PYMODULE'),
  ('pypdfium2._helpers.unsupported',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_helpers\\unsupported.py',
   'PYMODULE'),
  ('pypdfium2.version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\version.py',
   'PYMODULE'),
  ('pypdfium2._library_scope',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_library_scope.py',
   'PYMODULE'),
  ('pdfplumber.utils.text',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\utils\\text.py',
   'PYMODULE'),
  ('pdfplumber.utils.generic',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\utils\\generic.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pickletools', 'D:\\aaaaa\\Lib\\pickletools.py', 'PYMODULE'),
  ('pandas.core.arrays.interval',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('six',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('pandas.core.api',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.arrays',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pytz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pytz.lazy',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.io._util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('sqlite3', 'D:\\aaaaa\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.dump', 'D:\\aaaaa\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('sqlite3.__main__', 'D:\\aaaaa\\Lib\\sqlite3\\__main__.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'D:\\aaaaa\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('pandas.io.pytables',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.io.json',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.api.types',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.util.version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pandas.util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas._typing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.plotting',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.common',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.series',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.common',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.errors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas._libs',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.display',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pdfplumber.table',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\table.py',
   'PYMODULE'),
  ('pdfplumber.container',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\container.py',
   'PYMODULE'),
  ('pdfplumber.convert',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\convert.py',
   'PYMODULE'),
  ('pdfplumber._typing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\_typing.py',
   'PYMODULE'),
  ('pdfplumber._version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\_version.py',
   'PYMODULE'),
  ('pdfplumber.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\utils\\__init__.py',
   'PYMODULE'),
  ('pdfplumber.utils.pdfinternals',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\utils\\pdfinternals.py',
   'PYMODULE'),
  ('pandas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas._version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.io.api',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.html',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.testing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas._testing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pathlib', 'D:\\aaaaa\\Lib\\pathlib.py', 'PYMODULE'),
  ('threading', 'D:\\aaaaa\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'D:\\aaaaa\\Lib\\_threading_local.py', 'PYMODULE'),
  ('datetime', 'D:\\aaaaa\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\aaaaa\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('hashlib', 'D:\\aaaaa\\Lib\\hashlib.py', 'PYMODULE'),
  ('uuid', 'D:\\aaaaa\\Lib\\uuid.py', 'PYMODULE'),
  ('shutil', 'D:\\aaaaa\\Lib\\shutil.py', 'PYMODULE'),
  ('logging', 'D:\\aaaaa\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('tkinter.ttk', 'D:\\aaaaa\\Lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('tkinter.messagebox', 'D:\\aaaaa\\Lib\\tkinter\\messagebox.py', 'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\aaaaa\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.filedialog', 'D:\\aaaaa\\Lib\\tkinter\\filedialog.py', 'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\aaaaa\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.dialog', 'D:\\aaaaa\\Lib\\tkinter\\dialog.py', 'PYMODULE'),
  ('tkinter', 'D:\\aaaaa\\Lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.constants', 'D:\\aaaaa\\Lib\\tkinter\\constants.py', 'PYMODULE'),
  ('json', 'D:\\aaaaa\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'D:\\aaaaa\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'D:\\aaaaa\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\aaaaa\\Lib\\json\\scanner.py', 'PYMODULE')],
 [('pypdfium2_raw\\pdfium.dll',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2_raw\\pdfium.dll',
   'BINARY'),
  ('python312.dll', 'D:\\aaaaa\\python312.dll', 'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-860d95b1c38e637ce4509f5fa24fbf2a.dll',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-860d95b1c38e637ce4509f5fa24fbf2a.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-a4c2229bdc2a2a630acdc095b4d86008.dll',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy.libs\\msvcp140-a4c2229bdc2a2a630acdc095b4d86008.dll',
   'BINARY'),
  ('_decimal.pyd', 'D:\\aaaaa\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'D:\\aaaaa\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\aaaaa\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\aaaaa\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\aaaaa\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\aaaaa\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\aaaaa\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\aaaaa\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\aaaaa\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\aaaaa\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\aaaaa\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\aaaaa\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'D:\\aaaaa\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('PIL\\_webp.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_webp.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_imagingtk.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_avif.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_avif.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_imagingcms.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_imagingmath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\_philox.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\_common.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd', 'D:\\aaaaa\\DLLs\\_elementtree.pyd', 'EXTENSION'),
  ('PIL\\_imaging.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_imaging.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_imagingft.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\_cffi_backend.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\ops.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslib.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\writers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\index.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\interval.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\hashing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\arrays.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\window\\indexers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\missing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\groupby.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\join.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\reshape.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\parsers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd', 'D:\\aaaaa\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('pandas\\_libs\\json.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\json.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\internals.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\indexing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\sparse.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\hashtable.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\properties.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\lib.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\algos.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\pandas_datetime.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\pandas_parser.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\testing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\sas.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\byteswap.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'D:\\aaaaa\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('_tkinter.pyd', 'D:\\aaaaa\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'D:\\aaaaa\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\aaaaa\\VCRUNTIME140_1.dll', 'BINARY'),
  ('libssl-3.dll', 'D:\\aaaaa\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libcrypto-3.dll', 'D:\\aaaaa\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libffi-8.dll', 'D:\\aaaaa\\DLLs\\libffi-8.dll', 'BINARY'),
  ('python3.dll', 'D:\\aaaaa\\python3.dll', 'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'BINARY'),
  ('sqlite3.dll', 'D:\\aaaaa\\DLLs\\sqlite3.dll', 'BINARY'),
  ('tcl86t.dll', 'D:\\aaaaa\\DLLs\\tcl86t.dll', 'BINARY'),
  ('tk86t.dll', 'D:\\aaaaa\\DLLs\\tk86t.dll', 'BINARY'),
  ('ucrtbase.dll', 'E:\\a想\\bin\\ucrtbase.dll', 'BINARY'),
  ('zlib1.dll', 'D:\\aaaaa\\DLLs\\zlib1.dll', 'BINARY'),
  ('api-ms-win-core-sysinfo-l1-2-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-sysinfo-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-1.dll',
   'E:\\a想\\bin\\api-ms-win-core-fibers-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'E:\\a想\\bin\\api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'E:\\a想\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY')],
 [],
 [],
 [('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UTF32-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UTF32-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GB-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GB-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\83pv-RKSJ-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\83pv-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UCS2-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UCS2-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS2004-UTF8-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS2004-UTF8-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\to-unicode-Adobe-GB1.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\to-unicode-Adobe-GB1.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UTF16-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UTF16-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UTF32-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UTF32-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UTF32-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UTF32-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKdla-B5-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKdla-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSC-Johab-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSC-Johab-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\90msp-RKSJ-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\90msp-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\CNS2-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\CNS2-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GB-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GB-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\ETen-B5-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\ETen-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UTF16-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UTF16-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\NWP-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\NWP-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UCS2-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UCS2-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UTF16-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UTF16-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\B5pc-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\B5pc-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBT-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBT-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS2004-UTF32-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS2004-UTF32-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78ms-RKSJ-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\78ms-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UTF8-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UTF8-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBT-EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBT-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UCS2-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UCS2-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Hankaku-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Hankaku-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBK-EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBK-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\to-unicode-Adobe-CNS1.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\to-unicode-Adobe-CNS1.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Hiragana-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Hiragana-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\CNS1-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\CNS1-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS2004-UTF16-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS2004-UTF16-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSCms-UHC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSCms-UHC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UTF8-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UTF8-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSCpc-EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSCpc-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UCS2-HW-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UCS2-HW-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UTF32-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UTF32-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UTF32-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UTF32-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Ext-RKSJ-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Ext-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKdlb-B5-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKdlb-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS2004-UTF8-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS2004-UTF8-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSCms-UHC-HW-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSCms-UHC-HW-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBpc-EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBpc-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\90ms-RKSJ-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\90ms-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78ms-RKSJ-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\78ms-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKdlb-B5-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKdlb-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UCS2-HW-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UCS2-HW-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\WP-Symbol-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\WP-Symbol-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBKp-EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBKp-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBTpc-EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBTpc-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKdla-B5-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKdla-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\78-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UTF32-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UTF32-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\90msp-RKSJ-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\90msp-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Ext-RKSJ-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Ext-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\WP-Symbol-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\WP-Symbol-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UCS2-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UCS2-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJISX02132004-UTF32-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJISX02132004-UTF32-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBT-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBT-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\ETHK-B5-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\ETHK-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\CNS1-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\CNS1-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Add-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Add-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSCms-UHC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSCms-UHC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSC-EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSC-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\B5pc-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\B5pc-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Roman-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Roman-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Hiragana-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Hiragana-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UCS2-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UCS2-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBpc-EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBpc-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKgccs-B5-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKgccs-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKm314-B5-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKm314-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UTF16-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UTF16-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Roman-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Roman-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSCms-UHC-HW-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSCms-UHC-HW-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\B5-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GB-EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GB-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UTF8-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UTF8-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78-EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\78-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78-EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\78-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBKp-EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBKp-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKm471-B5-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKm471-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\B5-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\90pv-RKSJ-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\90pv-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UTF8-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UTF8-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UCS2-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UCS2-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJISX02132004-UTF32-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJISX02132004-UTF32-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSC-Johab-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSC-Johab-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\ETen-B5-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\ETen-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKm314-B5-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKm314-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\ETenms-B5-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\ETenms-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJISX0213-UTF32-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJISX0213-UTF32-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\to-unicode-Adobe-Japan1.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\to-unicode-Adobe-Japan1.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UTF16-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UTF16-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\ETHK-B5-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\ETHK-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UTF32-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UTF32-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UTF16-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UTF16-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78-RKSJ-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\78-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\CNS2-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\CNS2-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKgccs-B5-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKgccs-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJISX0213-UTF32-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJISX0213-UTF32-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78-RKSJ-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\78-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS2004-UTF32-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS2004-UTF32-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Katakana-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Katakana-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UTF16-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UTF16-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\CNS-EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\CNS-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS2004-UTF16-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS2004-UTF16-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSC-EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSC-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBTpc-EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBTpc-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\ETenms-B5-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\ETenms-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\78-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Add-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Add-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UTF16-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UTF16-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UTF8-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UTF8-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\RKSJ-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GB-EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GB-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UCS2-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UCS2-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\NWP-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\NWP-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBT-EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBT-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Hankaku-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Hankaku-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBK-EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBK-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Katakana-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Katakana-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSCpc-EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSCpc-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\RKSJ-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\to-unicode-Adobe-Korea1.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\to-unicode-Adobe-Korea1.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\README.txt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\README.txt',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UTF8-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UTF8-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Add-RKSJ-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Add-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKscs-B5-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKscs-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\py.typed',
   'DATA'),
  ('pdfminer\\cmap\\HKscs-B5-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKscs-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKm471-B5-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKm471-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBK2K-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBK2K-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UCS2-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UCS2-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBK2K-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBK2K-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Ext-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Ext-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\90pv-RKSJ-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\90pv-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\83pv-RKSJ-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\83pv-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UTF32-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UTF32-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\CNS-EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\CNS-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\90ms-RKSJ-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\90ms-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Ext-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Ext-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Add-RKSJ-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Add-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UTF8-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UTF8-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UTF8-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UTF8-V.pickle.gz',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE.APACHE',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE.BSD',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\INSTALLER',
   'DATA'),
  ('pypdfium2\\version.json',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\version.json',
   'DATA'),
  ('pypdfium2_raw\\version.json',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2_raw\\version.json',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\sh.msg', 'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tk_data\\icons.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tk_data\\menu.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\sq.msg', 'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\history.tcl', 'D:\\aaaaa\\tcl\\tcl8.6\\history.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\cs.msg', 'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tm.tcl', 'D:\\aaaaa\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tk_data\\megawidget.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\megawidget.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\EST', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\EST', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tk_data\\msgs\\es.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\es.msg', 'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\nl.msg', 'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\bn.msg', 'DATA'),
  ('_tcl_data\\tzdata\\UTC', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\UTC', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tk_data\\tearoff.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\tearoff.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\kw.msg', 'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tk_data\\msgs\\de.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\de.msg', 'DATA'),
  ('_tk_data\\spinbox.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\spinbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\MST', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\MST', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tk_data\\msgs\\el.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\el.msg', 'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Japan', 'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\GMT', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\sl.msg', 'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tk_data\\license.terms', 'D:\\aaaaa\\tcl\\tk8.6\\license.terms', 'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tk_data\\listbox.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\listbox.tcl', 'DATA'),
  ('_tk_data\\tclIndex', 'D:\\aaaaa\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\mk.msg', 'DATA'),
  ('_tcl_data\\msgs\\ja.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ja.msg', 'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ta.msg', 'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\gv.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\pkgIndex.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\package.tcl', 'D:\\aaaaa\\tcl\\tcl8.6\\package.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'D:\\aaaaa\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tk_data\\palette.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\palette.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\hi.msg', 'DATA'),
  ('_tcl_data\\msgs\\id.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\id.msg', 'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\CET', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\CET', 'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\th.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'D:\\aaaaa\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tk_data\\dialog.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\dialog.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\GMT-0', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\GMT-0', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\it.msg', 'DATA'),
  ('_tcl_data\\parray.tcl', 'D:\\aaaaa\\tcl\\tcl8.6\\parray.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Cuba', 'DATA'),
  ('_tcl_data\\msgs\\nn.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\nn.msg', 'DATA'),
  ('_tcl_data\\auto.tcl', 'D:\\aaaaa\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tk_data\\text.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\gl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tk_data\\images\\README', 'D:\\aaaaa\\tcl\\tk8.6\\images\\README', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Zulu', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Iran', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tk_data\\msgs\\da.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\da.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'D:\\aaaaa\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'D:\\aaaaa\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\el.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tk_data\\tkfbox.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\tkfbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\init.tcl', 'D:\\aaaaa\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\is.msg', 'DATA'),
  ('_tcl_data\\tzdata\\ROK', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\ROK', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\da.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\et.msg', 'DATA'),
  ('_tcl_data\\msgs\\mt.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\mt.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\be.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\pt.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Eire', 'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ga.msg', 'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\HST', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\HST', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Libya', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\eo.msg', 'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tk_data\\optMenu.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\optMenu.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\fa.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\ROC', 'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\nl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\bg.msg', 'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\WET', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\WET', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\te.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'D:\\aaaaa\\tcl\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'D:\\aaaaa\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\ttk\\entry.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\mkpsenc.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\lv.msg', 'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'D:\\aaaaa\\tcl\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\sr.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\fr.msg', 'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\tr.msg', 'DATA'),
  ('_tcl_data\\msgs\\he.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\he.msg', 'DATA'),
  ('_tk_data\\msgs\\it.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\it.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tk_data\\msgbox.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\msgbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ro.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\fi.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\sv.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\EET', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\EET', 'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tk_data\\tk.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'D:\\aaaaa\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\mr.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\PRC', 'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\nb.msg', 'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GB', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\GB', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\safe.tcl', 'D:\\aaaaa\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ar.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\uk.msg', 'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ca.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tk_data\\xmfbox.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\xmfbox.tcl', 'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tk_data\\iconlist.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\iconlist.tcl', 'DATA'),
  ('_tk_data\\ttk\\utils.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\ttk\\utils.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\hu.msg', 'DATA'),
  ('_tcl_data\\tzdata\\W-SU', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\W-SU', 'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tk_data\\button.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\button.tcl', 'DATA'),
  ('_tk_data\\msgs\\fi.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\fi.msg', 'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.5.tm',
   'D:\\aaaaa\\tcl\\tcl8\\8.5\\tcltest-2.5.5.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\sw.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\eu.msg', 'DATA'),
  ('_tcl_data\\msgs\\zh.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\zh.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tk_data\\choosedir.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\choosedir.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tk_data\\console.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\console.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\ru.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ru.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\hu.msg', 'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\sk.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tclIndex', 'D:\\aaaaa\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('_tk_data\\comdlg.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\comdlg.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'D:\\aaaaa\\tcl\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Egypt', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\NZ', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\GMT0', 'DATA'),
  ('_tk_data\\bgerror.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\bgerror.tcl', 'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\ttk\\fonts.tcl', 'DATA'),
  ('_tk_data\\msgs\\en.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\en.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\GMT+0', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\clock.tcl', 'D:\\aaaaa\\tcl\\tcl8.6\\clock.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\fo.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tk_data\\obsolete.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\obsolete.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\UCT', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\UCT', 'DATA'),
  ('_tk_data\\msgs\\cs.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\cs.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\hr.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\ttk\\ttk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\MET', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\MET', 'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\vi.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\sv.msg', 'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\word.tcl', 'D:\\aaaaa\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tk_data\\scale.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\lt.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\de.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tk_data\\entry.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tk_data\\safetk.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\safetk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\af.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\pt.msg', 'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\kl.msg', 'DATA'),
  ('_tk_data\\msgs\\pl.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\pl.msg', 'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ko.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'D:\\aaaaa\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ms.msg', 'DATA'),
  ('_tk_data\\focus.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('_tk_data\\ttk\\scale.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\ttk\\scale.tcl', 'DATA'),
  ('_tcl_data\\msgs\\kok.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\kok.msg', 'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\eo.msg', 'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tk_data\\scrlbar.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\scrlbar.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\pl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\fr.msg', 'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tk_data\\clrpick.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\clrpick.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('numpy-2.3.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy-2.3.2.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.3.2.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy-2.3.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.3.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy-2.3.2.dist-info\\WHEEL',
   'DATA'),
  ('pdfminer_six-20250506.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer_six-20250506.dist-info\\top_level.txt',
   'DATA'),
  ('pdfminer_six-20250506.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer_six-20250506.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('numpy-2.3.2.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy-2.3.2.dist-info\\entry_points.txt',
   'DATA'),
  ('pdfminer_six-20250506.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer_six-20250506.dist-info\\RECORD',
   'DATA'),
  ('pdfminer_six-20250506.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer_six-20250506.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.3.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy-2.3.2.dist-info\\METADATA',
   'DATA'),
  ('pdfminer_six-20250506.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer_six-20250506.dist-info\\WHEEL',
   'DATA'),
  ('pdfminer_six-20250506.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer_six-20250506.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.3.2.dist-info\\DELVEWHEEL',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy-2.3.2.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.3.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy-2.3.2.dist-info\\INSTALLER',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\7.29\\build\\图片整理工具\\base_library.zip',
   'DATA')],
 [('sre_parse', 'D:\\aaaaa\\Lib\\sre_parse.py', 'PYMODULE'),
  ('heapq', 'D:\\aaaaa\\Lib\\heapq.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\aaaaa\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec', 'D:\\aaaaa\\Lib\\encodings\\uu_codec.py', 'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\aaaaa\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8', 'D:\\aaaaa\\Lib\\encodings\\utf_8.py', 'PYMODULE'),
  ('encodings.utf_7', 'D:\\aaaaa\\Lib\\encodings\\utf_7.py', 'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\aaaaa\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\aaaaa\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32', 'D:\\aaaaa\\Lib\\encodings\\utf_32.py', 'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\aaaaa\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\aaaaa\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16', 'D:\\aaaaa\\Lib\\encodings\\utf_16.py', 'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\aaaaa\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\aaaaa\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620', 'D:\\aaaaa\\Lib\\encodings\\tis_620.py', 'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\aaaaa\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\aaaaa\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\aaaaa\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13', 'D:\\aaaaa\\Lib\\encodings\\rot_13.py', 'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\aaaaa\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\aaaaa\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode', 'D:\\aaaaa\\Lib\\encodings\\punycode.py', 'PYMODULE'),
  ('encodings.ptcp154', 'D:\\aaaaa\\Lib\\encodings\\ptcp154.py', 'PYMODULE'),
  ('encodings.palmos', 'D:\\aaaaa\\Lib\\encodings\\palmos.py', 'PYMODULE'),
  ('encodings.oem', 'D:\\aaaaa\\Lib\\encodings\\oem.py', 'PYMODULE'),
  ('encodings.mbcs', 'D:\\aaaaa\\Lib\\encodings\\mbcs.py', 'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\aaaaa\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\aaaaa\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\aaaaa\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\aaaaa\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\aaaaa\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\aaaaa\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\aaaaa\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\aaaaa\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\aaaaa\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\aaaaa\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1', 'D:\\aaaaa\\Lib\\encodings\\latin_1.py', 'PYMODULE'),
  ('encodings.kz1048', 'D:\\aaaaa\\Lib\\encodings\\kz1048.py', 'PYMODULE'),
  ('encodings.koi8_u', 'D:\\aaaaa\\Lib\\encodings\\koi8_u.py', 'PYMODULE'),
  ('encodings.koi8_t', 'D:\\aaaaa\\Lib\\encodings\\koi8_t.py', 'PYMODULE'),
  ('encodings.koi8_r', 'D:\\aaaaa\\Lib\\encodings\\koi8_r.py', 'PYMODULE'),
  ('encodings.johab', 'D:\\aaaaa\\Lib\\encodings\\johab.py', 'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\aaaaa\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\aaaaa\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\aaaaa\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\aaaaa\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\aaaaa\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\aaaaa\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\aaaaa\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\aaaaa\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\aaaaa\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\aaaaa\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\aaaaa\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\aaaaa\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\aaaaa\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\aaaaa\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\aaaaa\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\aaaaa\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\aaaaa\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\aaaaa\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\aaaaa\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\aaaaa\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\aaaaa\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\aaaaa\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna', 'D:\\aaaaa\\Lib\\encodings\\idna.py', 'PYMODULE'),
  ('encodings.hz', 'D:\\aaaaa\\Lib\\encodings\\hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\aaaaa\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\aaaaa\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk', 'D:\\aaaaa\\Lib\\encodings\\gbk.py', 'PYMODULE'),
  ('encodings.gb2312', 'D:\\aaaaa\\Lib\\encodings\\gb2312.py', 'PYMODULE'),
  ('encodings.gb18030', 'D:\\aaaaa\\Lib\\encodings\\gb18030.py', 'PYMODULE'),
  ('encodings.euc_kr', 'D:\\aaaaa\\Lib\\encodings\\euc_kr.py', 'PYMODULE'),
  ('encodings.euc_jp', 'D:\\aaaaa\\Lib\\encodings\\euc_jp.py', 'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\aaaaa\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\aaaaa\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950', 'D:\\aaaaa\\Lib\\encodings\\cp950.py', 'PYMODULE'),
  ('encodings.cp949', 'D:\\aaaaa\\Lib\\encodings\\cp949.py', 'PYMODULE'),
  ('encodings.cp932', 'D:\\aaaaa\\Lib\\encodings\\cp932.py', 'PYMODULE'),
  ('encodings.cp875', 'D:\\aaaaa\\Lib\\encodings\\cp875.py', 'PYMODULE'),
  ('encodings.cp874', 'D:\\aaaaa\\Lib\\encodings\\cp874.py', 'PYMODULE'),
  ('encodings.cp869', 'D:\\aaaaa\\Lib\\encodings\\cp869.py', 'PYMODULE'),
  ('encodings.cp866', 'D:\\aaaaa\\Lib\\encodings\\cp866.py', 'PYMODULE'),
  ('encodings.cp865', 'D:\\aaaaa\\Lib\\encodings\\cp865.py', 'PYMODULE'),
  ('encodings.cp864', 'D:\\aaaaa\\Lib\\encodings\\cp864.py', 'PYMODULE'),
  ('encodings.cp863', 'D:\\aaaaa\\Lib\\encodings\\cp863.py', 'PYMODULE'),
  ('encodings.cp862', 'D:\\aaaaa\\Lib\\encodings\\cp862.py', 'PYMODULE'),
  ('encodings.cp861', 'D:\\aaaaa\\Lib\\encodings\\cp861.py', 'PYMODULE'),
  ('encodings.cp860', 'D:\\aaaaa\\Lib\\encodings\\cp860.py', 'PYMODULE'),
  ('encodings.cp858', 'D:\\aaaaa\\Lib\\encodings\\cp858.py', 'PYMODULE'),
  ('encodings.cp857', 'D:\\aaaaa\\Lib\\encodings\\cp857.py', 'PYMODULE'),
  ('encodings.cp856', 'D:\\aaaaa\\Lib\\encodings\\cp856.py', 'PYMODULE'),
  ('encodings.cp855', 'D:\\aaaaa\\Lib\\encodings\\cp855.py', 'PYMODULE'),
  ('encodings.cp852', 'D:\\aaaaa\\Lib\\encodings\\cp852.py', 'PYMODULE'),
  ('encodings.cp850', 'D:\\aaaaa\\Lib\\encodings\\cp850.py', 'PYMODULE'),
  ('encodings.cp775', 'D:\\aaaaa\\Lib\\encodings\\cp775.py', 'PYMODULE'),
  ('encodings.cp737', 'D:\\aaaaa\\Lib\\encodings\\cp737.py', 'PYMODULE'),
  ('encodings.cp720', 'D:\\aaaaa\\Lib\\encodings\\cp720.py', 'PYMODULE'),
  ('encodings.cp500', 'D:\\aaaaa\\Lib\\encodings\\cp500.py', 'PYMODULE'),
  ('encodings.cp437', 'D:\\aaaaa\\Lib\\encodings\\cp437.py', 'PYMODULE'),
  ('encodings.cp424', 'D:\\aaaaa\\Lib\\encodings\\cp424.py', 'PYMODULE'),
  ('encodings.cp273', 'D:\\aaaaa\\Lib\\encodings\\cp273.py', 'PYMODULE'),
  ('encodings.cp1258', 'D:\\aaaaa\\Lib\\encodings\\cp1258.py', 'PYMODULE'),
  ('encodings.cp1257', 'D:\\aaaaa\\Lib\\encodings\\cp1257.py', 'PYMODULE'),
  ('encodings.cp1256', 'D:\\aaaaa\\Lib\\encodings\\cp1256.py', 'PYMODULE'),
  ('encodings.cp1255', 'D:\\aaaaa\\Lib\\encodings\\cp1255.py', 'PYMODULE'),
  ('encodings.cp1254', 'D:\\aaaaa\\Lib\\encodings\\cp1254.py', 'PYMODULE'),
  ('encodings.cp1253', 'D:\\aaaaa\\Lib\\encodings\\cp1253.py', 'PYMODULE'),
  ('encodings.cp1252', 'D:\\aaaaa\\Lib\\encodings\\cp1252.py', 'PYMODULE'),
  ('encodings.cp1251', 'D:\\aaaaa\\Lib\\encodings\\cp1251.py', 'PYMODULE'),
  ('encodings.cp1250', 'D:\\aaaaa\\Lib\\encodings\\cp1250.py', 'PYMODULE'),
  ('encodings.cp1140', 'D:\\aaaaa\\Lib\\encodings\\cp1140.py', 'PYMODULE'),
  ('encodings.cp1125', 'D:\\aaaaa\\Lib\\encodings\\cp1125.py', 'PYMODULE'),
  ('encodings.cp1026', 'D:\\aaaaa\\Lib\\encodings\\cp1026.py', 'PYMODULE'),
  ('encodings.cp1006', 'D:\\aaaaa\\Lib\\encodings\\cp1006.py', 'PYMODULE'),
  ('encodings.cp037', 'D:\\aaaaa\\Lib\\encodings\\cp037.py', 'PYMODULE'),
  ('encodings.charmap', 'D:\\aaaaa\\Lib\\encodings\\charmap.py', 'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\aaaaa\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\aaaaa\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5', 'D:\\aaaaa\\Lib\\encodings\\big5.py', 'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\aaaaa\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii', 'D:\\aaaaa\\Lib\\encodings\\ascii.py', 'PYMODULE'),
  ('encodings.aliases', 'D:\\aaaaa\\Lib\\encodings\\aliases.py', 'PYMODULE'),
  ('encodings', 'D:\\aaaaa\\Lib\\encodings\\__init__.py', 'PYMODULE'),
  ('weakref', 'D:\\aaaaa\\Lib\\weakref.py', 'PYMODULE'),
  ('re._parser', 'D:\\aaaaa\\Lib\\re\\_parser.py', 'PYMODULE'),
  ('re._constants', 'D:\\aaaaa\\Lib\\re\\_constants.py', 'PYMODULE'),
  ('re._compiler', 'D:\\aaaaa\\Lib\\re\\_compiler.py', 'PYMODULE'),
  ('re._casefix', 'D:\\aaaaa\\Lib\\re\\_casefix.py', 'PYMODULE'),
  ('stat', 'D:\\aaaaa\\Lib\\stat.py', 'PYMODULE'),
  ('sre_constants', 'D:\\aaaaa\\Lib\\sre_constants.py', 'PYMODULE'),
  ('keyword', 'D:\\aaaaa\\Lib\\keyword.py', 'PYMODULE'),
  ('sre_compile', 'D:\\aaaaa\\Lib\\sre_compile.py', 'PYMODULE'),
  ('collections.abc', 'D:\\aaaaa\\Lib\\collections\\abc.py', 'PYMODULE'),
  ('enum', 'D:\\aaaaa\\Lib\\enum.py', 'PYMODULE'),
  ('functools', 'D:\\aaaaa\\Lib\\functools.py', 'PYMODULE'),
  ('genericpath', 'D:\\aaaaa\\Lib\\genericpath.py', 'PYMODULE'),
  ('codecs', 'D:\\aaaaa\\Lib\\codecs.py', 'PYMODULE'),
  ('warnings', 'D:\\aaaaa\\Lib\\warnings.py', 'PYMODULE'),
  ('reprlib', 'D:\\aaaaa\\Lib\\reprlib.py', 'PYMODULE'),
  ('operator', 'D:\\aaaaa\\Lib\\operator.py', 'PYMODULE'),
  ('linecache', 'D:\\aaaaa\\Lib\\linecache.py', 'PYMODULE'),
  ('locale', 'D:\\aaaaa\\Lib\\locale.py', 'PYMODULE'),
  ('_weakrefset', 'D:\\aaaaa\\Lib\\_weakrefset.py', 'PYMODULE'),
  ('_collections_abc', 'D:\\aaaaa\\Lib\\_collections_abc.py', 'PYMODULE'),
  ('ntpath', 'D:\\aaaaa\\Lib\\ntpath.py', 'PYMODULE'),
  ('copyreg', 'D:\\aaaaa\\Lib\\copyreg.py', 'PYMODULE'),
  ('abc', 'D:\\aaaaa\\Lib\\abc.py', 'PYMODULE'),
  ('posixpath', 'D:\\aaaaa\\Lib\\posixpath.py', 'PYMODULE'),
  ('types', 'D:\\aaaaa\\Lib\\types.py', 'PYMODULE'),
  ('traceback', 'D:\\aaaaa\\Lib\\traceback.py', 'PYMODULE'),
  ('collections', 'D:\\aaaaa\\Lib\\collections\\__init__.py', 'PYMODULE'),
  ('io', 'D:\\aaaaa\\Lib\\io.py', 'PYMODULE'),
  ('os', 'D:\\aaaaa\\Lib\\os.py', 'PYMODULE'),
  ('re', 'D:\\aaaaa\\Lib\\re\\__init__.py', 'PYMODULE')])
