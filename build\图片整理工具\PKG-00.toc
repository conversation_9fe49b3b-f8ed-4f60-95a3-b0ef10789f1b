('C:\\Users\\<USER>\\Desktop\\7.29\\build\\图片整理工具\\图片整理工具.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\build\\图片整理工具\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\7.29\\build\\图片整理工具\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\7.29\\build\\图片整理工具\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\build\\图片整理工具\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\build\\图片整理工具\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\7.29\\build\\图片整理工具\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('my_script',
   'C:\\Users\\<USER>\\Desktop\\7.29\\my_script.py',
   'PYSOURCE'),
  ('pypdfium2_raw\\pdfium.dll',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2_raw\\pdfium.dll',
   'BINARY'),
  ('python312.dll', 'D:\\aaaaa\\python312.dll', 'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-860d95b1c38e637ce4509f5fa24fbf2a.dll',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-860d95b1c38e637ce4509f5fa24fbf2a.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-a4c2229bdc2a2a630acdc095b4d86008.dll',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy.libs\\msvcp140-a4c2229bdc2a2a630acdc095b4d86008.dll',
   'BINARY'),
  ('select.pyd', 'D:\\aaaaa\\DLLs\\select.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\aaaaa\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\aaaaa\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\aaaaa\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'D:\\aaaaa\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'D:\\aaaaa\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\aaaaa\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\aaaaa\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\aaaaa\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\aaaaa\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\aaaaa\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\aaaaa\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'D:\\aaaaa\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('PIL\\_webp.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_webp.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_imagingtk.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_imagingcms.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\_philox.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\_common.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_imagingmath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\_cffi_backend.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_imaging.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_imagingft.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd', 'D:\\aaaaa\\DLLs\\_elementtree.pyd', 'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\ops.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslib.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\writers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\index.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\interval.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\hashing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\arrays.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\window\\indexers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\missing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\groupby.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\join.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\reshape.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\parsers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd', 'D:\\aaaaa\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('pandas\\_libs\\json.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\json.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\internals.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\indexing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\sparse.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\hashtable.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\properties.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\lib.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\algos.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\pandas_datetime.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\pandas_parser.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\testing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\sas.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\byteswap.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'D:\\aaaaa\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('_tkinter.pyd', 'D:\\aaaaa\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'D:\\aaaaa\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\aaaaa\\VCRUNTIME140_1.dll', 'BINARY'),
  ('libssl-3.dll', 'D:\\aaaaa\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libcrypto-3.dll', 'D:\\aaaaa\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libffi-8.dll', 'D:\\aaaaa\\DLLs\\libffi-8.dll', 'BINARY'),
  ('python3.dll', 'D:\\aaaaa\\python3.dll', 'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'BINARY'),
  ('sqlite3.dll', 'D:\\aaaaa\\DLLs\\sqlite3.dll', 'BINARY'),
  ('tk86t.dll', 'D:\\aaaaa\\DLLs\\tk86t.dll', 'BINARY'),
  ('tcl86t.dll', 'D:\\aaaaa\\DLLs\\tcl86t.dll', 'BINARY'),
  ('ucrtbase.dll', 'E:\\a想\\bin\\ucrtbase.dll', 'BINARY'),
  ('zlib1.dll', 'D:\\aaaaa\\DLLs\\zlib1.dll', 'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'E:\\a想\\bin\\api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-1.dll',
   'E:\\a想\\bin\\api-ms-win-core-fibers-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-2-0.dll',
   'E:\\a想\\bin\\api-ms-win-core-sysinfo-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'E:\\a想\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UTF32-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UTF32-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UCS2-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UCS2-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSC-Johab-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSC-Johab-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBpc-EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBpc-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Hiragana-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Hiragana-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GB-EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GB-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJISX02132004-UTF32-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJISX02132004-UTF32-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBK-EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBK-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\to-unicode-Adobe-GB1.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\to-unicode-Adobe-GB1.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\WP-Symbol-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\WP-Symbol-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSC-EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSC-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\ETen-B5-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\ETen-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\B5pc-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\B5pc-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\90pv-RKSJ-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\90pv-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKm471-B5-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKm471-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKdlb-B5-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKdlb-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Hankaku-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Hankaku-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UCS2-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UCS2-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UTF8-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UTF8-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Add-RKSJ-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Add-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\ETHK-B5-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\ETHK-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS2004-UTF16-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS2004-UTF16-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GB-EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GB-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKm314-B5-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKm314-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UTF32-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UTF32-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UTF8-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UTF8-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKgccs-B5-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKgccs-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKdla-B5-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKdla-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Ext-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Ext-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UTF16-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UTF16-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBT-EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBT-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSCms-UHC-HW-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSCms-UHC-HW-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UCS2-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UCS2-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\to-unicode-Adobe-CNS1.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\to-unicode-Adobe-CNS1.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS2004-UTF8-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS2004-UTF8-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Hankaku-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Hankaku-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKm471-B5-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKm471-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UCS2-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UCS2-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UTF8-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UTF8-V.pickle.gz',
   'DATA'),
  ('pdfminer\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\py.typed',
   'DATA'),
  ('pdfminer\\cmap\\GBKp-EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBKp-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\NWP-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\NWP-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJISX0213-UTF32-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJISX0213-UTF32-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\CNS2-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\CNS2-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Roman-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Roman-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UTF32-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UTF32-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSCms-UHC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSCms-UHC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\CNS1-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\CNS1-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UCS2-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UCS2-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\B5-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UTF32-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UTF32-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSCms-UHC-HW-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSCms-UHC-HW-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBT-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBT-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UTF8-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UTF8-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Ext-RKSJ-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Ext-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKdla-B5-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKdla-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\WP-Symbol-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\WP-Symbol-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\ETen-B5-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\ETen-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78-EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\78-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\ETenms-B5-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\ETenms-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UTF32-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UTF32-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKscs-B5-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKscs-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\CNS1-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\CNS1-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\B5pc-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\B5pc-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\RKSJ-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS2004-UTF16-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS2004-UTF16-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Katakana-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Katakana-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UTF8-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UTF8-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBTpc-EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBTpc-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\README.txt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\README.txt',
   'DATA'),
  ('pdfminer\\cmap\\78ms-RKSJ-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\78ms-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\90ms-RKSJ-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\90ms-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UTF32-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UTF32-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UTF8-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UTF8-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSCpc-EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSCpc-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\78-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UCS2-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UCS2-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJISX02132004-UTF32-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJISX02132004-UTF32-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\ETenms-B5-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\ETenms-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UCS2-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UCS2-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBTpc-EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBTpc-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\RKSJ-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKgccs-B5-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKgccs-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKdlb-B5-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKdlb-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS2004-UTF32-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS2004-UTF32-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78ms-RKSJ-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\78ms-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Ext-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Ext-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UTF16-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UTF16-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UCS2-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UCS2-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UCS2-HW-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UCS2-HW-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Add-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Add-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UTF32-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UTF32-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\to-unicode-Adobe-Korea1.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\to-unicode-Adobe-Korea1.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Katakana-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Katakana-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\83pv-RKSJ-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\83pv-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UTF16-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UTF16-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKm314-B5-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKm314-B5-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Hiragana-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Hiragana-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBK2K-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBK2K-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBK-EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBK-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78-RKSJ-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\78-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Ext-RKSJ-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Ext-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBpc-EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBpc-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\B5-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS2004-UTF8-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS2004-UTF8-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSCms-UHC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSCms-UHC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSCpc-EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSCpc-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\HKscs-B5-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\HKscs-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78-EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\78-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\90msp-RKSJ-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\90msp-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBT-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBT-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78-RKSJ-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\78-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\83pv-RKSJ-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\83pv-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\90ms-RKSJ-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\90ms-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UTF16-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UTF16-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\CNS-EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\CNS-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS2004-UTF32-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS2004-UTF32-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBT-EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBT-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UTF32-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UTF32-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GB-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GB-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJISX0213-UTF32-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJISX0213-UTF32-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniGB-UTF16-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniGB-UTF16-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Roman-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Roman-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniCNS-UTF16-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniCNS-UTF16-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\90pv-RKSJ-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\90pv-RKSJ-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UTF8-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UTF8-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\CNS-EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\CNS-EUC-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSC-Johab-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSC-Johab-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UTF8-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UTF8-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniKS-UTF16-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniKS-UTF16-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Add-RKSJ-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Add-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GB-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GB-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UTF16-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UTF16-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\90msp-RKSJ-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\90msp-RKSJ-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\NWP-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\NWP-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\KSC-EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\KSC-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\Add-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\Add-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\78-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\78-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\ETHK-B5-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\ETHK-B5-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\CNS2-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\CNS2-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\UniJIS-UCS2-HW-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\UniJIS-UCS2-HW-H.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\to-unicode-Adobe-Japan1.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\to-unicode-Adobe-Japan1.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBKp-EUC-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBKp-EUC-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\GBK2K-V.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\GBK2K-V.pickle.gz',
   'DATA'),
  ('pdfminer\\cmap\\EUC-H.pickle.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmap\\EUC-H.pickle.gz',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE.APACHE',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE.BSD',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.5.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography-45.0.5.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('pypdfium2\\version.json',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\version.json',
   'DATA'),
  ('pypdfium2_raw\\version.json',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2_raw\\version.json',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\sr.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tk_data\\optMenu.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\optMenu.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\is.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\eo.msg', 'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'D:\\aaaaa\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tk_data\\scrlbar.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\scrlbar.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tk_data\\safetk.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\safetk.tcl', 'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\ttk\\ttk.tcl', 'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\sl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\th.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\sw.msg', 'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'D:\\aaaaa\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\auto.tcl', 'D:\\aaaaa\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('_tk_data\\msgs\\es.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\es.msg', 'DATA'),
  ('_tk_data\\msgs\\sv.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\sv.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tk_data\\listbox.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\listbox.tcl', 'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'D:\\aaaaa\\tcl\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tk_data\\button.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\button.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ru.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\lv.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tk_data\\msgs\\de.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\de.msg', 'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Zulu', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tk_data\\tk.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\gv.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\nn.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\nl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\tr.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tk_data\\spinbox.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\spinbox.tcl', 'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Egypt', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'D:\\aaaaa\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ca.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\hr.msg', 'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\fr.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\ROK', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ar.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\MET', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\MET', 'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ro.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\fi.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tk_data\\menu.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\mk.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\PRC', 'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tk_data\\msgs\\it.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\it.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\de.msg', 'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\id.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ko.msg', 'DATA'),
  ('_tk_data\\bgerror.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\bgerror.tcl', 'DATA'),
  ('_tcl_data\\msgs\\af.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\af.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\ttk\\utils.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\UCT', 'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\W-SU', 'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\eo.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\eu.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\pkgIndex.tcl', 'DATA'),
  ('_tcl_data\\msgs\\fa.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\fa.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\el.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\cs.msg', 'DATA'),
  ('_tcl_data\\msgs\\fi.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\fi.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\sh.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ms.msg', 'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'D:\\aaaaa\\tcl\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\UTC', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tk_data\\console.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\console.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tk_data\\clrpick.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\clrpick.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\it.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\ttk\\fonts.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tm.tcl', 'D:\\aaaaa\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tclIndex', 'D:\\aaaaa\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('_tcl_data\\msgs\\kw.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\kw.msg', 'DATA'),
  ('_tcl_data\\history.tcl', 'D:\\aaaaa\\tcl\\tcl8.6\\history.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tk_data\\text.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tk_data\\dialog.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\dialog.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tk_data\\images\\README', 'D:\\aaaaa\\tcl\\tk8.6\\images\\README', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\fo.msg', 'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tk_data\\tclIndex', 'D:\\aaaaa\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tk_data\\msgs\\da.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\da.msg', 'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tk_data\\megawidget.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\megawidget.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\gl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tk_data\\tkfbox.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\tkfbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tk_data\\iconlist.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\iconlist.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Eire', 'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es.msg', 'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tk_data\\palette.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\palette.tcl', 'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tk_data\\entry.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\mr.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\GMT', 'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tk_data\\tearoff.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\tearoff.tcl', 'DATA'),
  ('_tk_data\\msgs\\pl.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\pl.msg', 'DATA'),
  ('_tk_data\\msgs\\cs.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\cs.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ta.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tk_data\\msgs\\en.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\en.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tk_data\\license.terms', 'D:\\aaaaa\\tcl\\tk8.6\\license.terms', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\sq.msg', 'DATA'),
  ('_tcl_data\\msgs\\pl.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\pl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\parray.tcl', 'D:\\aaaaa\\tcl\\tcl8.6\\parray.tcl', 'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\uk.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\be.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'D:\\aaaaa\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\CET', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\CET', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'D:\\aaaaa\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\hu.msg', 'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\GMT-0', 'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\EST', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\EST', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\kl.msg', 'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tk_data\\focus.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\word.tcl', 'D:\\aaaaa\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tk_data\\xmfbox.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\xmfbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\MST', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\MST', 'DATA'),
  ('_tcl_data\\tzdata\\GB', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\GB', 'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\HST', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\HST', 'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\bn.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tk_data\\scale.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tk_data\\icons.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('_tcl_data\\msgs\\bg.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\bg.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tk_data\\comdlg.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\comdlg.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Libya', 'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\pt.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Cuba', 'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\clock.tcl', 'D:\\aaaaa\\tcl\\tcl8.6\\clock.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'D:\\aaaaa\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'D:\\aaaaa\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\safe.tcl', 'D:\\aaaaa\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Japan', 'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\te.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\zh.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\package.tcl', 'D:\\aaaaa\\tcl\\tcl8.6\\package.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\ROC', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\ROC', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\lt.msg', 'DATA'),
  ('_tcl_data\\msgs\\sk.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\sk.msg', 'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.5.tm',
   'D:\\aaaaa\\tcl\\tcl8\\8.5\\tcltest-2.5.5.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\ttk\\scale.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\da.msg', 'DATA'),
  ('_tcl_data\\tzdata\\WET', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\WET', 'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\et.msg', 'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\pt.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\NZ', 'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\ru.msg', 'DATA'),
  ('_tk_data\\msgbox.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\msgbox.tcl', 'DATA'),
  ('_tcl_data\\msgs\\ga.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ga.msg', 'DATA'),
  ('_tk_data\\ttk\\entry.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\ttk\\entry.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tk_data\\obsolete.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\obsolete.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'D:\\aaaaa\\tcl\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\hi.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\kok.msg', 'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\GMT0', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\fr.msg', 'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\init.tcl', 'D:\\aaaaa\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\hu.msg', 'DATA'),
  ('_tcl_data\\msgs\\ja.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ja.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\vi.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tk_data\\msgs\\el.msg', 'D:\\aaaaa\\tcl\\tk8.6\\msgs\\el.msg', 'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tk_data\\choosedir.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\choosedir.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'D:\\aaaaa\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\nl.msg', 'DATA'),
  ('_tcl_data\\msgs\\mt.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\mt.msg', 'DATA'),
  ('_tcl_data\\tzdata\\GMT+0', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\GMT+0', 'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\he.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl', 'D:\\aaaaa\\tcl\\tk8.6\\mkpsenc.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Iran', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\nb.msg', 'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\EET', 'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\EET', 'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'D:\\aaaaa\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'D:\\aaaaa\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg', 'D:\\aaaaa\\tcl\\tcl8.6\\msgs\\sv.msg', 'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'D:\\aaaaa\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('numpy-2.3.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy-2.3.2.dist-info\\METADATA',
   'DATA'),
  ('pdfminer_six-20250506.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer_six-20250506.dist-info\\top_level.txt',
   'DATA'),
  ('pdfminer_six-20250506.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer_six-20250506.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('numpy-2.3.2.dist-info\\DELVEWHEEL',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy-2.3.2.dist-info\\DELVEWHEEL',
   'DATA'),
  ('pdfminer_six-20250506.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer_six-20250506.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.3.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy-2.3.2.dist-info\\WHEEL',
   'DATA'),
  ('pdfminer_six-20250506.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer_six-20250506.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.3.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy-2.3.2.dist-info\\RECORD',
   'DATA'),
  ('pdfminer_six-20250506.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer_six-20250506.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.3.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy-2.3.2.dist-info\\INSTALLER',
   'DATA'),
  ('pdfminer_six-20250506.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer_six-20250506.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.3.2.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy-2.3.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.3.2.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy-2.3.2.dist-info\\entry_points.txt',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\7.29\\build\\图片整理工具\\base_library.zip',
   'DATA')],
 'python312.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
