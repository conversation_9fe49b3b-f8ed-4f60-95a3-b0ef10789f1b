('C:\\Users\\<USER>\\Desktop\\7.29\\build\\图片整理工具\\PYZ-00.pyz',
 [('PIL',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyPDF2',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\__init__.py',
   'PYMODULE'),
  ('PyPDF2._cmap',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_cmap.py',
   'PYMODULE'),
  ('PyPDF2._codecs',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_codecs\\__init__.py',
   'PYMODULE'),
  ('PyPDF2._codecs.adobe_glyphs',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_codecs\\adobe_glyphs.py',
   'PYMODULE'),
  ('PyPDF2._codecs.pdfdoc',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_codecs\\pdfdoc.py',
   'PYMODULE'),
  ('PyPDF2._codecs.std',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_codecs\\std.py',
   'PYMODULE'),
  ('PyPDF2._codecs.symbol',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_codecs\\symbol.py',
   'PYMODULE'),
  ('PyPDF2._codecs.zapfding',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_codecs\\zapfding.py',
   'PYMODULE'),
  ('PyPDF2._encryption',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_encryption.py',
   'PYMODULE'),
  ('PyPDF2._merger',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_merger.py',
   'PYMODULE'),
  ('PyPDF2._page',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_page.py',
   'PYMODULE'),
  ('PyPDF2._protocols',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_protocols.py',
   'PYMODULE'),
  ('PyPDF2._reader',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_reader.py',
   'PYMODULE'),
  ('PyPDF2._security',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_security.py',
   'PYMODULE'),
  ('PyPDF2._utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_utils.py',
   'PYMODULE'),
  ('PyPDF2._version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_version.py',
   'PYMODULE'),
  ('PyPDF2._writer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\_writer.py',
   'PYMODULE'),
  ('PyPDF2.constants',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\constants.py',
   'PYMODULE'),
  ('PyPDF2.errors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\errors.py',
   'PYMODULE'),
  ('PyPDF2.filters',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\filters.py',
   'PYMODULE'),
  ('PyPDF2.generic',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\generic\\__init__.py',
   'PYMODULE'),
  ('PyPDF2.generic._annotations',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\generic\\_annotations.py',
   'PYMODULE'),
  ('PyPDF2.generic._base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\generic\\_base.py',
   'PYMODULE'),
  ('PyPDF2.generic._data_structures',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\generic\\_data_structures.py',
   'PYMODULE'),
  ('PyPDF2.generic._fit',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\generic\\_fit.py',
   'PYMODULE'),
  ('PyPDF2.generic._outline',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\generic\\_outline.py',
   'PYMODULE'),
  ('PyPDF2.generic._rectangle',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\generic\\_rectangle.py',
   'PYMODULE'),
  ('PyPDF2.generic._utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\generic\\_utils.py',
   'PYMODULE'),
  ('PyPDF2.pagerange',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\pagerange.py',
   'PYMODULE'),
  ('PyPDF2.papersizes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\papersizes.py',
   'PYMODULE'),
  ('PyPDF2.types',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\types.py',
   'PYMODULE'),
  ('PyPDF2.xmp',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\PyPDF2\\xmp.py',
   'PYMODULE'),
  ('__future__', 'D:\\aaaaa\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\aaaaa\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\aaaaa\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\aaaaa\\Lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\aaaaa\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\aaaaa\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\aaaaa\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_sitebuiltins', 'D:\\aaaaa\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'D:\\aaaaa\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\aaaaa\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'D:\\aaaaa\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\aaaaa\\Lib\\ast.py', 'PYMODULE'),
  ('backports',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'D:\\aaaaa\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\aaaaa\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\aaaaa\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\aaaaa\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\aaaaa\\Lib\\calendar.py', 'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd', 'D:\\aaaaa\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\aaaaa\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\aaaaa\\Lib\\codeop.py', 'PYMODULE'),
  ('colorsys', 'D:\\aaaaa\\Lib\\colorsys.py', 'PYMODULE'),
  ('concurrent', 'D:\\aaaaa\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'D:\\aaaaa\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\aaaaa\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\aaaaa\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\aaaaa\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'D:\\aaaaa\\Lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'D:\\aaaaa\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\aaaaa\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\aaaaa\\Lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('csv', 'D:\\aaaaa\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\aaaaa\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\aaaaa\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\aaaaa\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'D:\\aaaaa\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\aaaaa\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\aaaaa\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\aaaaa\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'D:\\aaaaa\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes', 'D:\\aaaaa\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('dataclasses', 'D:\\aaaaa\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\aaaaa\\Lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal', 'D:\\aaaaa\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\aaaaa\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\aaaaa\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'D:\\aaaaa\\Lib\\doctest.py', 'PYMODULE'),
  ('email', 'D:\\aaaaa\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\aaaaa\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\aaaaa\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\aaaaa\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'D:\\aaaaa\\Lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\aaaaa\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\aaaaa\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\aaaaa\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\aaaaa\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\aaaaa\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\aaaaa\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\aaaaa\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\aaaaa\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\aaaaa\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\aaaaa\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\aaaaa\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\aaaaa\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\aaaaa\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\aaaaa\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\aaaaa\\Lib\\email\\utils.py', 'PYMODULE'),
  ('et_xmlfile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fileinput', 'D:\\aaaaa\\Lib\\fileinput.py', 'PYMODULE'),
  ('fnmatch', 'D:\\aaaaa\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\aaaaa\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\aaaaa\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\aaaaa\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\aaaaa\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\aaaaa\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\aaaaa\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\aaaaa\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\aaaaa\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\aaaaa\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\aaaaa\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\aaaaa\\Lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'D:\\aaaaa\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\aaaaa\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\aaaaa\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.server', 'D:\\aaaaa\\Lib\\http\\server.py', 'PYMODULE'),
  ('importlib', 'D:\\aaaaa\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\aaaaa\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\aaaaa\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\aaaaa\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\aaaaa\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\aaaaa\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers', 'D:\\aaaaa\\Lib\\importlib\\readers.py', 'PYMODULE'),
  ('importlib.resources',
   'D:\\aaaaa\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\aaaaa\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\aaaaa\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\aaaaa\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\aaaaa\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\aaaaa\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\aaaaa\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\aaaaa\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\aaaaa\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\aaaaa\\Lib\\ipaddress.py', 'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('json', 'D:\\aaaaa\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\aaaaa\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\aaaaa\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\aaaaa\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'D:\\aaaaa\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'D:\\aaaaa\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'D:\\aaaaa\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\aaaaa\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\aaaaa\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\aaaaa\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\aaaaa\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\aaaaa\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\aaaaa\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\aaaaa\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\aaaaa\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\aaaaa\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\aaaaa\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\aaaaa\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\aaaaa\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\aaaaa\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\aaaaa\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\aaaaa\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\aaaaa\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\aaaaa\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\aaaaa\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\aaaaa\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\aaaaa\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\aaaaa\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\aaaaa\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\aaaaa\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\aaaaa\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\aaaaa\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\aaaaa\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\ctypeslib\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib._ctypeslib',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\ctypeslib\\_ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._format_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_format_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'D:\\aaaaa\\Lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib', 'D:\\aaaaa\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\aaaaa\\Lib\\pdb.py', 'PYMODULE'),
  ('pdfminer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\__init__.py',
   'PYMODULE'),
  ('pdfminer._saslprep',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\_saslprep.py',
   'PYMODULE'),
  ('pdfminer.arcfour',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\arcfour.py',
   'PYMODULE'),
  ('pdfminer.ascii85',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\ascii85.py',
   'PYMODULE'),
  ('pdfminer.casting',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\casting.py',
   'PYMODULE'),
  ('pdfminer.ccitt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\ccitt.py',
   'PYMODULE'),
  ('pdfminer.cmapdb',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\cmapdb.py',
   'PYMODULE'),
  ('pdfminer.converter',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\converter.py',
   'PYMODULE'),
  ('pdfminer.data_structures',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\data_structures.py',
   'PYMODULE'),
  ('pdfminer.encodingdb',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\encodingdb.py',
   'PYMODULE'),
  ('pdfminer.fontmetrics',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\fontmetrics.py',
   'PYMODULE'),
  ('pdfminer.glyphlist',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\glyphlist.py',
   'PYMODULE'),
  ('pdfminer.image',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\image.py',
   'PYMODULE'),
  ('pdfminer.jbig2',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\jbig2.py',
   'PYMODULE'),
  ('pdfminer.latin_enc',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\latin_enc.py',
   'PYMODULE'),
  ('pdfminer.layout',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\layout.py',
   'PYMODULE'),
  ('pdfminer.lzw',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\lzw.py',
   'PYMODULE'),
  ('pdfminer.pdfcolor',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\pdfcolor.py',
   'PYMODULE'),
  ('pdfminer.pdfdevice',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\pdfdevice.py',
   'PYMODULE'),
  ('pdfminer.pdfdocument',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\pdfdocument.py',
   'PYMODULE'),
  ('pdfminer.pdfexceptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\pdfexceptions.py',
   'PYMODULE'),
  ('pdfminer.pdffont',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\pdffont.py',
   'PYMODULE'),
  ('pdfminer.pdfinterp',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\pdfinterp.py',
   'PYMODULE'),
  ('pdfminer.pdfpage',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\pdfpage.py',
   'PYMODULE'),
  ('pdfminer.pdfparser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\pdfparser.py',
   'PYMODULE'),
  ('pdfminer.pdftypes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\pdftypes.py',
   'PYMODULE'),
  ('pdfminer.psexceptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\psexceptions.py',
   'PYMODULE'),
  ('pdfminer.psparser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\psparser.py',
   'PYMODULE'),
  ('pdfminer.runlength',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\runlength.py',
   'PYMODULE'),
  ('pdfminer.settings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\settings.py',
   'PYMODULE'),
  ('pdfminer.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfminer\\utils.py',
   'PYMODULE'),
  ('pdfplumber',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\__init__.py',
   'PYMODULE'),
  ('pdfplumber._typing',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\_typing.py',
   'PYMODULE'),
  ('pdfplumber._version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\_version.py',
   'PYMODULE'),
  ('pdfplumber.container',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\container.py',
   'PYMODULE'),
  ('pdfplumber.convert',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\convert.py',
   'PYMODULE'),
  ('pdfplumber.display',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\display.py',
   'PYMODULE'),
  ('pdfplumber.page',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\page.py',
   'PYMODULE'),
  ('pdfplumber.pdf',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\pdf.py',
   'PYMODULE'),
  ('pdfplumber.repair',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\repair.py',
   'PYMODULE'),
  ('pdfplumber.structure',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\structure.py',
   'PYMODULE'),
  ('pdfplumber.table',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\table.py',
   'PYMODULE'),
  ('pdfplumber.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\utils\\__init__.py',
   'PYMODULE'),
  ('pdfplumber.utils.clustering',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\utils\\clustering.py',
   'PYMODULE'),
  ('pdfplumber.utils.exceptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\utils\\exceptions.py',
   'PYMODULE'),
  ('pdfplumber.utils.generic',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\utils\\generic.py',
   'PYMODULE'),
  ('pdfplumber.utils.geometry',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\utils\\geometry.py',
   'PYMODULE'),
  ('pdfplumber.utils.pdfinternals',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\utils\\pdfinternals.py',
   'PYMODULE'),
  ('pdfplumber.utils.text',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pdfplumber\\utils\\text.py',
   'PYMODULE'),
  ('pickle', 'D:\\aaaaa\\Lib\\pickle.py', 'PYMODULE'),
  ('pickletools', 'D:\\aaaaa\\Lib\\pickletools.py', 'PYMODULE'),
  ('pkgutil', 'D:\\aaaaa\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\aaaaa\\Lib\\platform.py', 'PYMODULE'),
  ('pprint', 'D:\\aaaaa\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\aaaaa\\Lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'D:\\aaaaa\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'D:\\aaaaa\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics', 'D:\\aaaaa\\Lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('pypdfium2',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\__init__.py',
   'PYMODULE'),
  ('pypdfium2._helpers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_helpers\\__init__.py',
   'PYMODULE'),
  ('pypdfium2._helpers.attachment',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_helpers\\attachment.py',
   'PYMODULE'),
  ('pypdfium2._helpers.bitmap',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_helpers\\bitmap.py',
   'PYMODULE'),
  ('pypdfium2._helpers.document',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_helpers\\document.py',
   'PYMODULE'),
  ('pypdfium2._helpers.matrix',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_helpers\\matrix.py',
   'PYMODULE'),
  ('pypdfium2._helpers.misc',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_helpers\\misc.py',
   'PYMODULE'),
  ('pypdfium2._helpers.page',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_helpers\\page.py',
   'PYMODULE'),
  ('pypdfium2._helpers.pageobjects',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_helpers\\pageobjects.py',
   'PYMODULE'),
  ('pypdfium2._helpers.textpage',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_helpers\\textpage.py',
   'PYMODULE'),
  ('pypdfium2._helpers.unsupported',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_helpers\\unsupported.py',
   'PYMODULE'),
  ('pypdfium2._library_scope',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\_library_scope.py',
   'PYMODULE'),
  ('pypdfium2.internal',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\internal\\__init__.py',
   'PYMODULE'),
  ('pypdfium2.internal.bases',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\internal\\bases.py',
   'PYMODULE'),
  ('pypdfium2.internal.consts',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\internal\\consts.py',
   'PYMODULE'),
  ('pypdfium2.internal.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\internal\\utils.py',
   'PYMODULE'),
  ('pypdfium2.raw',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\raw.py',
   'PYMODULE'),
  ('pypdfium2.version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2\\version.py',
   'PYMODULE'),
  ('pypdfium2_raw',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2_raw\\__init__.py',
   'PYMODULE'),
  ('pypdfium2_raw.bindings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pypdfium2_raw\\bindings.py',
   'PYMODULE'),
  ('pyperclip',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('pytz',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue', 'D:\\aaaaa\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\aaaaa\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\aaaaa\\Lib\\random.py', 'PYMODULE'),
  ('rlcompleter', 'D:\\aaaaa\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'D:\\aaaaa\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\aaaaa\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\aaaaa\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\aaaaa\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\aaaaa\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\aaaaa\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\aaaaa\\Lib\\site.py', 'PYMODULE'),
  ('six',
   'C:\\Users\\<USER>\\Desktop\\7.29\\clean_env\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('socket', 'D:\\aaaaa\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'D:\\aaaaa\\Lib\\socketserver.py', 'PYMODULE'),
  ('sqlite3', 'D:\\aaaaa\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.__main__', 'D:\\aaaaa\\Lib\\sqlite3\\__main__.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'D:\\aaaaa\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('sqlite3.dump', 'D:\\aaaaa\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('ssl', 'D:\\aaaaa\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\aaaaa\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\aaaaa\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\aaaaa\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\aaaaa\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\aaaaa\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\aaaaa\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\aaaaa\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\aaaaa\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\aaaaa\\Lib\\threading.py', 'PYMODULE'),
  ('tkinter', 'D:\\aaaaa\\Lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\aaaaa\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants', 'D:\\aaaaa\\Lib\\tkinter\\constants.py', 'PYMODULE'),
  ('tkinter.dialog', 'D:\\aaaaa\\Lib\\tkinter\\dialog.py', 'PYMODULE'),
  ('tkinter.filedialog', 'D:\\aaaaa\\Lib\\tkinter\\filedialog.py', 'PYMODULE'),
  ('tkinter.messagebox', 'D:\\aaaaa\\Lib\\tkinter\\messagebox.py', 'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\aaaaa\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk', 'D:\\aaaaa\\Lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('token', 'D:\\aaaaa\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\aaaaa\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib', 'D:\\aaaaa\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'D:\\aaaaa\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._re', 'D:\\aaaaa\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('tomllib._types', 'D:\\aaaaa\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\aaaaa\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'D:\\aaaaa\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\aaaaa\\Lib\\typing.py', 'PYMODULE'),
  ('urllib', 'D:\\aaaaa\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'D:\\aaaaa\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\aaaaa\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'D:\\aaaaa\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'D:\\aaaaa\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('uuid', 'D:\\aaaaa\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'D:\\aaaaa\\Lib\\webbrowser.py', 'PYMODULE'),
  ('xml', 'D:\\aaaaa\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom', 'D:\\aaaaa\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('xml.dom.NodeFilter', 'D:\\aaaaa\\Lib\\xml\\dom\\NodeFilter.py', 'PYMODULE'),
  ('xml.dom.domreg', 'D:\\aaaaa\\Lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\aaaaa\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat', 'D:\\aaaaa\\Lib\\xml\\dom\\minicompat.py', 'PYMODULE'),
  ('xml.dom.minidom', 'D:\\aaaaa\\Lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'D:\\aaaaa\\Lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.xmlbuilder', 'D:\\aaaaa\\Lib\\xml\\dom\\xmlbuilder.py', 'PYMODULE'),
  ('xml.etree', 'D:\\aaaaa\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\aaaaa\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\aaaaa\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\aaaaa\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\aaaaa\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers', 'D:\\aaaaa\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat', 'D:\\aaaaa\\Lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.sax', 'D:\\aaaaa\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\aaaaa\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\aaaaa\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'D:\\aaaaa\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'D:\\aaaaa\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader', 'D:\\aaaaa\\Lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('xmlrpc', 'D:\\aaaaa\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'D:\\aaaaa\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('zipfile', 'D:\\aaaaa\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path', 'D:\\aaaaa\\Lib\\zipfile\\_path\\__init__.py', 'PYMODULE'),
  ('zipfile._path.glob', 'D:\\aaaaa\\Lib\\zipfile\\_path\\glob.py', 'PYMODULE'),
  ('zipimport', 'D:\\aaaaa\\Lib\\zipimport.py', 'PYMODULE')])
