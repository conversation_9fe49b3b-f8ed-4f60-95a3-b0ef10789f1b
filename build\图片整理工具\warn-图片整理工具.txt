
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named urllib.urlopen - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named urllib.urlencode - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named posix - imported by os (conditional, optional), shutil (conditional), importlib._bootstrap_external (conditional), posixpath (optional)
missing module named resource - imported by posix (top-level), fsspec.asyn (conditional, optional), torch._inductor.codecache (delayed, conditional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by tty (top-level), psutil._compat (delayed, optional), getpass (optional), tqdm.utils (delayed, optional), absl.flags._helpers (optional), click._termui_impl (conditional), werkzeug._reloader (delayed, optional), accelerate.commands.menu.keymap (delayed, conditional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), http.server (delayed, optional), psutil (optional), netrc (delayed, conditional), getpass (delayed), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional), setuptools._vendor.backports.tarfile (optional)
excluded module named matplotlib - imported by mpl_toolkits.mplot3d.axes3d (top-level), mpl_toolkits.mplot3d.art3d (top-level), mpl_toolkits.mplot3d.proj3d (top-level), mpl_toolkits.mplot3d.axis3d (top-level), sympy.testing.runtests (delayed, conditional), pandas.io.formats.style (optional), tqdm.gui (delayed), gradio.utils (delayed, optional), networkx.drawing.nx_pylab (delayed), nltk.parse.dependencygraph (delayed, conditional), nltk.tokenize.texttiling (delayed), nltk.app (optional), nltk.app.wordfreq_app (top-level), keras.src.saving.file_editor (delayed, optional), scipy.stats._fit (delayed, optional), scipy.stats._survival (delayed, optional), pandas.plotting._matplotlib.core (top-level), pandas.plotting._matplotlib.tools (top-level), pandas.plotting._matplotlib.misc (top-level), pandas.plotting._matplotlib.style (top-level), pandas.plotting._matplotlib.timeseries (delayed)
missing module named 'matplotlib.pyplot' - imported by scipy.spatial._plotutils (delayed), torch.profiler._memory_profiler (delayed), pandas.io.formats.style (optional), tqdm.gui (delayed), torch.utils.tensorboard._utils (delayed), gradio.utils (delayed), nltk.probability (delayed, optional), nltk.draw.dispersion (delayed, conditional, optional), networkx.drawing.nx_pylab (delayed), torch._functorch.partitioners (delayed, conditional), scipy.stats._fit (delayed, conditional), scipy.stats._survival (delayed, conditional), torch.ao.quantization.fx._model_report.model_report_visualizer (optional), torch.distributed._tools.memory_tracker (delayed), pandas.plotting._matplotlib.tools (delayed), pandas.plotting._matplotlib.style (delayed), pandas.plotting._matplotlib.misc (delayed), pandas.plotting._matplotlib.core (delayed), pandas.plotting._matplotlib.boxplot (delayed), pandas.plotting._matplotlib.hist (delayed), pandas.plotting._matplotlib (delayed)
missing module named regex.DEFAULT_VERSION - imported by regex (delayed, optional), regex.regex (delayed, optional)
missing module named nltk.corpus.CorpusReader - imported by nltk.corpus (delayed, conditional), nltk.corpus.reader.wordnet (delayed, conditional)
missing module named nltk.corpus.reader.CorpusReader - imported by nltk.corpus.reader (top-level), nltk.corpus.reader.wordnet (top-level), nltk.corpus.reader.lin (top-level), nltk.corpus.reader.sentiwordnet (top-level), nltk.corpus.reader.crubadan (top-level), nltk.corpus.reader.bcp47 (top-level)
missing module named startup - imported by pyreadline3.keysyms.common (conditional), pyreadline3.keysyms.keysyms (conditional)
missing module named sets - imported by pyreadline3.keysyms.common (optional), pytz.tzinfo (optional)
missing module named System - imported by pyreadline3.clipboard.ironpython_clipboard (top-level), pyreadline3.keysyms.ironpython_keysyms (top-level), pyreadline3.console.ironpython_console (top-level), pyreadline3.rlmain (conditional)
missing module named console - imported by pyreadline3.console.ansi (conditional)
missing module named clr - imported by pyreadline3.clipboard.ironpython_clipboard (top-level), pyreadline3.console.ironpython_console (top-level)
missing module named IronPythonConsole - imported by pyreadline3.console.ironpython_console (top-level)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional), cpuinfo.cpuinfo (delayed, optional), pygments.formatters.img (optional)
missing module named StringIO - imported by six (conditional), urllib3.packages.six (conditional), botocore.vendored.six (conditional), pydub.audio_segment (optional), nltk.corpus.reader.timit (delayed, optional)
missing module named OpenGL - imported by pygame (delayed)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), wheel.vendored.packaging._manylinux (delayed, optional)
missing module named packaging.licenses - imported by packaging (optional), setuptools.config._validate_pyproject.formats (optional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named _typeshed - imported by setuptools._distutils.dist (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), pkg_resources (conditional), git.objects.fun (conditional), trio._file_io (conditional), trio._path (conditional), anyio.abc._eventloop (conditional), anyio._core._sockets (conditional), anyio._core._fileio (conditional), anyio._core._tempfile (conditional), httpx._transports.wsgi (conditional), pydantic._internal._dataclasses (conditional), torch.utils._backport_slots (conditional), anyio._backends._asyncio (conditional), anyio._core._asyncio_selector_thread (conditional), anyio._backends._trio (conditional)
missing module named jnius - imported by platformdirs.android (delayed, conditional, optional)
missing module named android - imported by platformdirs.android (delayed, conditional, optional)
missing module named 'unittest.mock' - imported by setuptools._distutils.compilers.C.msvc (top-level), torch._guards (top-level), torch._dispatch.python (top-level), torch._dynamo.symbolic_convert (top-level), torch._functorch.aot_autograd (top-level), torch._functorch._aot_autograd.traced_function_transforms (top-level), torch._export (top-level), torch._dynamo.eval_frame (top-level), torch._dynamo.testing (top-level), torch.utils.data.datapipes.dataframe.dataframes (delayed), torch.testing._internal.common_utils (top-level), torch._inductor.ir (top-level), torch._inductor.dependencies (top-level), torch._inductor.ops_handler (top-level), torch._inductor.codegen.cpp_utils (top-level), torch._inductor.debug (top-level), torch._inductor.lowering (top-level), torch._inductor.select_algorithm (top-level), torch._inductor.codegen.cuda.cuda_template (top-level), torch._inductor.codegen.cpp_gemm_template (top-level), torch._inductor.codegen.cpp_template (top-level), torch._inductor.codegen.cuda.cutlass_utils (delayed), torch._inductor.codegen.rocm.rocm_template (top-level), torch._dynamo.backends.common (top-level), torch._dynamo.test_minifier_common (top-level), torch._inductor.codegen.cuda.cutlass_epilogue_gen (top-level), torch.testing._internal.common_distributed (top-level), torch.testing._internal.logging_utils (top-level)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), setuptools._distutils.archive_util (optional), setuptools._vendor.backports.tarfile (optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level), transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed), joblib.externals.loky.backend.fork_exec (delayed)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level), joblib.externals.loky.backend.context (top-level), transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level), numba.testing.main (optional), joblib.parallel (top-level)
missing module named fcntl - imported by subprocess (optional), psutil._compat (delayed, optional), xmlrpc.server (optional), tqdm.utils (delayed, optional), absl.flags._helpers (optional), pty (delayed, optional), filelock._unix (conditional, optional), torch.testing._internal.distributed.distributed_test (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.cpu_count - imported by multiprocessing (top-level), transformers.data.processors.squad (top-level)
missing module named multiprocessing.Pool - imported by multiprocessing (top-level), transformers.models.nougat.tokenization_nougat_fast (top-level), transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (top-level), transformers.data.processors.squad (top-level), torchvision.datasets.kinetics (top-level), scipy._lib._util (delayed, conditional)
missing module named multiprocessing.Pipe - imported by multiprocessing (top-level), uvicorn.supervisors.multiprocess (top-level)
missing module named multiprocessing.Lock - imported by multiprocessing (top-level), gradio.flagging (top-level)
missing module named multiprocessing.Value - imported by multiprocessing (top-level), werkzeug.debug (top-level)
missing module named multiprocessing.Queue - imported by multiprocessing (delayed), cpuinfo.cpuinfo (delayed)
missing module named multiprocessing.Process - imported by multiprocessing (delayed), cpuinfo.cpuinfo (delayed)
missing module named multiprocessing.freeze_support - imported by multiprocessing (conditional), numba.runtests (conditional)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named dl - imported by setuptools.command.build_ext (conditional, optional)
missing module named 'Cython.Distutils' - imported by setuptools.command.build_ext (conditional, optional)
missing module named 'packaging.licenses' - imported by setuptools._normalization (optional)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional), tqdm.cli (delayed, conditional, optional)
missing module named jaraco.text.yield_lines - imported by setuptools._vendor.jaraco.text (top-level), setuptools._entry_points (top-level), setuptools.command._requirestxt (top-level)
missing module named pygame.register_quit - imported by pygame (top-level), pygame.fastevent (top-level)
missing module named pygame.error - imported by pygame (top-level), pygame.fastevent (top-level)
missing module named numpy.ndarray - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.lib.recfunctions (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level), pandas.compat.numpy.function (top-level), pytesseract.pytesseract (optional), pygame.surfarray (top-level), scipy.io._mmio (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._mstats_basic (top-level), scipy.stats._mstats_extras (top-level)
missing module named numpy.uint32 - imported by numpy (top-level), numpy.array_api._typing (top-level), pygame.surfarray (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.array - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), scipy.stats._stats_py (top-level), scipy.linalg._decomp (top-level), scipy.linalg._decomp_schur (top-level), scipy.sparse.linalg._isolve.utils (top-level), scipy.optimize._lbfgsb_py (top-level), scipy.optimize._tnc (top-level), scipy.optimize._slsqp_py (top-level), scipy.interpolate._interpolate (top-level), scipy.interpolate._fitpack_impl (top-level), scipy.interpolate._fitpack2 (top-level), numba.cuda.vectorizers (top-level), nltk.parse.transitionparser (optional), pygame.surfarray (top-level), scipy.io._netcdf (top-level), scipy.integrate._ode (top-level), scipy._lib._finite_differences (top-level), scipy.stats._morestats (top-level), scipy.signal._bsplines (top-level), scipy.signal._filter_design (top-level), scipy.signal._lti_conversion (top-level)
missing module named 'pygame.overlay' - imported by pygame (optional)
missing module named 'pygame.cdrom' - imported by pygame (conditional, optional)
missing module named ossaudiodev - imported by nltk.corpus.reader.timit (delayed, optional)
missing module named numpy.arccosh - imported by numpy (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.arcsinh - imported by numpy (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.tan - imported by numpy (top-level), scipy.signal._bsplines (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.power - imported by numpy (top-level), scipy.stats._kde (top-level)
missing module named numpy.sinh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.cosh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.tanh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.expm1 - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.log1p - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.ceil - imported by numpy (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.log - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._morestats (top-level), scipy.signal._waveforms (top-level)
missing module named numpy.logical_and - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named numpy.complex128 - imported by numpy (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.complex64 - imported by numpy (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.e - imported by numpy (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.greater - imported by numpy (top-level), scipy.optimize._minpack_py (top-level), scipy.signal._bsplines (top-level)
missing module named numpy.sign - imported by numpy (top-level), scipy.linalg._matfuncs (top-level)
missing module named numpy.conjugate - imported by numpy (top-level), scipy.linalg._matfuncs (top-level), scipy.signal._filter_design (top-level)
missing module named numpy.logical_not - imported by numpy (top-level), scipy.linalg._matfuncs (top-level)
missing module named numpy.single - imported by numpy (top-level), scipy.linalg._decomp_schur (top-level)
missing module named numpy.floor - imported by numpy (top-level), scipy.special._basic (top-level), scipy.special._orthogonal (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.signal._bsplines (top-level)
missing module named numpy.arcsin - imported by numpy (top-level), scipy.linalg._decomp_svd (top-level)
missing module named numpy.arccos - imported by numpy (top-level), scipy.linalg._decomp_svd (top-level), scipy.special._orthogonal (top-level)
missing module named numpy.conj - imported by numpy (top-level), scipy.linalg._decomp (top-level), scipy.io._mmio (top-level)
missing module named numpy.inexact - imported by numpy (top-level), scipy.linalg._decomp (top-level), scipy.special._basic (top-level), scipy.optimize._minpack_py (top-level)
missing module named _dummy_thread - imported by numpy.core.arrayprint (optional), cffi.lock (conditional, optional), sortedcontainers.sortedlist (conditional, optional), torch._jit_internal (optional)
missing module named dummy_threading - imported by psutil._compat (optional), requests.cookies (optional), joblib.compressor (optional)
missing module named numpy.core.result_type - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.float_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.number - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.object_ - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.max - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.all - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.errstate - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.bool_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.inf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isnan - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.array2string - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.imag - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.real - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.iscomplexobj - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.signbit - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isscalar - imported by numpy.core (delayed), numpy.testing._private.utils (delayed), numpy.lib.polynomial (top-level)
missing module named numpy.core.array - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.isnat - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.ndarray - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.array_repr - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.arange - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.empty - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.float32 - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.intp - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level)
missing module named 'unittest.case' - imported by numpy.testing._private.utils (top-level), absl.testing.absltest (conditional)
excluded module named unittest - imported by numpy.testing (top-level), doctest (top-level), numpy.testing._private.utils (top-level), torch.utils._config_module (top-level), torch._inductor.compile_fx (top-level), torch._inductor.utils (top-level), numba.testing (top-level), numba.testing.main (top-level), numba.testing.loader (top-level), numba.tests (top-level), numba.tests.support (top-level), numba.cuda.testing (top-level), git.util (delayed, conditional, optional), torch._dynamo.trace_rules (top-level), torch._dynamo.testing (top-level), torch._dynamo.variables.tensor (top-level), tensorflow.python.autograph.impl.conversion (top-level), torch.testing._internal.common_utils (top-level), torch.testing._internal.common_device_type (top-level), tensorflow.python.distribute.combinations (top-level), httplib2.iri2uri (conditional), tensorflow.python.distribute.multi_process_runner (top-level), tensorflow.python.distribute.multi_process_lib (top-level), tensorflow.python.platform.test (top-level), tensorflow.python.framework.test_util (top-level), absl.testing.parameterized (top-level), absl.testing.absltest (top-level), absl.testing._pretty_print_reporter (top-level), absl.testing.xml_reporter (top-level), tensorflow.python.distribute.multi_worker_test_base (top-level), tensorflow.python.framework.test_combinations (top-level), tensorflow.python.distribute.strategy_combinations (top-level), torch.testing._internal.optests.generate_tests (top-level), torch._dynamo.backends.distributed (top-level), pdfminer.ccitt (delayed, conditional), tensorflow.python.autograph.utils.testing (top-level), tensorflow.python.keras.keras_parameterized (top-level), keras.src.testing.test_case (top-level), torch._inductor.aoti_eager (top-level), torch._numpy.testing.utils (top-level), torch.fx.passes.tests.test_pass_manager (top-level), torch.testing._internal.opinfo.core (top-level), torch.testing._internal.opinfo.definitions._masked (top-level), torch.testing._internal.common_methods_invocations (top-level), torch.testing._internal.opinfo.definitions.linalg (top-level), torch.testing._internal.opinfo.definitions.special (top-level), torch.testing._internal.opinfo.definitions.fft (top-level), torch.testing._internal.opinfo.definitions.signal (top-level), torch.testing._internal.common_distributed (top-level), torch.testing._internal.common_fsdp (top-level), torch.testing._internal.common_modules (top-level), torch.testing._internal.common_nn (top-level), torch.testing._internal.common_optimizers (top-level), torch.testing._internal.common_quantization (top-level), torch.testing._internal.distributed.distributed_test (top-level), torch.testing._internal.distributed.rpc.rpc_test (top-level), torch.testing._internal.distributed.rpc_utils (top-level), torch.testing._internal.hop_db (top-level), torch.testing._internal.inductor_utils (top-level), torch.testing._internal.torchbind_impls (delayed), torch.testing._internal.triton_utils (top-level)
missing module named numpy.core.linspace - imported by numpy.core (top-level), numpy.lib.index_tricks (top-level)
missing module named numpy.core.iinfo - imported by numpy.core (top-level), numpy.lib.twodim_base (top-level)
missing module named numpy.core.transpose - imported by numpy.core (top-level), numpy.lib.function_base (top-level)
missing module named numpy.random.RandomState - imported by numpy.random (delayed), networkx.utils.backends (delayed)
missing module named numpy.core.asarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.utils (top-level), numpy.fft._pocketfft (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.integer - imported by numpy.core (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.sqrt - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.conjugate - imported by numpy.core (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.swapaxes - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.zeros - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy._typing._ufunc - imported by numpy._typing (conditional)
missing module named numpy.core.reciprocal - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.argsort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sign - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.count_nonzero - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.divide - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.matmul - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.asanyarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.atleast_2d - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.prod - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amax - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amin - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.moveaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.geterrobj - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.finfo - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.isfinite - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sum - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.multiply - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.add - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.dot - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.Inf - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.newaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.complexfloating - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.inexact - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.cdouble - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.csingle - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.double - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.single - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intc - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty_like - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named threadpoolctl - imported by numpy.lib.utils (delayed, optional), scipy.io._fast_matrix_market (optional)
missing module named numpy.core.ufunc - imported by numpy.core (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.ones - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.hstack - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_1d - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_3d - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.vstack - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named pickle5 - imported by numpy.compat.py3k (optional)
missing module named numpy.eye - imported by numpy (delayed), numpy.core.numeric (delayed), scipy.optimize._optimize (top-level), scipy.linalg._decomp (top-level), scipy.interpolate._pade (top-level), scipy.signal._lti_conversion (top-level)
missing module named numpy.recarray - imported by numpy (top-level), numpy.lib.recfunctions (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.expand_dims - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.iscomplexobj - imported by numpy (top-level), numpy.ma.core (top-level), scipy.linalg._decomp (top-level), scipy.linalg._decomp_ldl (top-level)
missing module named numpy.amin - imported by numpy (top-level), numpy.ma.core (top-level), scipy.stats._morestats (top-level)
missing module named numpy.amax - imported by numpy (top-level), numpy.ma.core (top-level), scipy.linalg._matfuncs (top-level), scipy.stats._morestats (top-level)
missing module named numpy.isinf - imported by numpy (top-level), numpy.testing._private.utils (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named numpy.isnan - imported by numpy (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.isfinite - imported by numpy (top-level), numpy.testing._private.utils (top-level), scipy.linalg._decomp (top-level), scipy.linalg._matfuncs (top-level), scipy.optimize._slsqp_py (top-level)
missing module named numpy.float64 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy.optimize._lbfgsb_py (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), scipy.stats._mstats_extras (top-level)
missing module named numpy.float32 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.uint64 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.uint16 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.uint8 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.int64 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.int32 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.int16 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.int8 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.bytes_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.str_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.void - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.object_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.datetime64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.timedelta64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.number - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.complexfloating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.floating - imported by numpy (top-level), numpy._typing._array_like (top-level), torch._dynamo.variables.misc (optional)
missing module named numpy.integer - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ctypeslib (top-level)
missing module named numpy.unsignedinteger - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.bool_ - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.mrecords (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.generic - imported by numpy (top-level), numpy._typing._array_like (top-level), torch._dynamo.variables.misc (optional)
missing module named numpy.dtype - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.array_api._typing (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level), scipy.optimize._minpack_py (top-level), scipy.io._netcdf (top-level), torch._dynamo.variables.misc (optional)
missing module named numpy.ufunc - imported by numpy (top-level), numpy._typing (top-level), numpy.testing.overrides (top-level)
missing module named numpy.histogramdd - imported by numpy (delayed), numpy.lib.twodim_base (delayed)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
missing module named pydot - imported by networkx.drawing.nx_pydot (delayed), keras.src.utils.model_visualization (optional), torch._functorch.partitioners (delayed), torch.fx.passes.graph_drawer (optional), tensorflow.python.keras.utils.vis_utils (optional)
missing module named pygraphviz - imported by networkx.drawing.nx_agraph (delayed, optional)
missing module named 'matplotlib.cm' - imported by networkx.drawing.nx_pylab (delayed)
missing module named 'matplotlib.colors' - imported by mpl_toolkits.mplot3d.axes3d (top-level), mpl_toolkits.mplot3d.art3d (top-level), pandas.io.formats.style (conditional), pandas.plotting._misc (conditional), networkx.drawing.nx_pylab (delayed), pandas.plotting._matplotlib.style (top-level), pandas.plotting._matplotlib.core (delayed)
missing module named 'matplotlib.patches' - imported by mpl_toolkits.mplot3d.axes3d (top-level), mpl_toolkits.mplot3d.art3d (top-level), networkx.drawing.nx_pylab (delayed)
missing module named 'matplotlib.path' - imported by networkx.drawing.nx_pylab (delayed)
missing module named 'matplotlib.collections' - imported by mpl_toolkits.mplot3d.axes3d (top-level), mpl_toolkits.mplot3d.art3d (top-level), scipy.spatial._plotutils (delayed), networkx.drawing.nx_pylab (delayed)
missing module named 'dask.array' - imported by scipy._lib.array_api_compat.common._helpers (delayed), scipy._lib.array_api_compat.common._aliases (delayed, conditional), scipy._lib.array_api_compat.dask.array (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named cupy - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), opt_einsum.backends.cupy (delayed), scipy._lib.array_api_compat.common._aliases (delayed, conditional), scipy._lib.array_api_compat.cupy (top-level), scipy._lib.array_api_compat.cupy._aliases (top-level), scipy._lib._array_api (delayed, conditional)
missing module named google.protobuf.pyext._message - imported by google.protobuf.pyext (conditional, optional), google.protobuf.internal.api_implementation (conditional, optional), google.protobuf.descriptor (conditional), google.protobuf.pyext.cpp_message (conditional)
missing module named google.protobuf.enable_deterministic_proto_serialization - imported by google.protobuf (optional), google.protobuf.internal.api_implementation (optional)
missing module named google.protobuf.internal._api_implementation - imported by google.protobuf.internal (optional), google.protobuf.internal.api_implementation (optional)
missing module named 'tensorflow.compat' - imported by tensorboard.util.op_evaluator (delayed), tensorboard.util.encoder (delayed), tensorboard.plugins.audio.summary (delayed), tensorboard.plugins.custom_scalar.summary (delayed), tensorboard.plugins.histogram.summary (delayed), tensorboard.plugins.image.summary (delayed), tensorboard.plugins.pr_curve.summary (delayed), tensorboard.plugins.scalar.summary (delayed), tensorboard.plugins.text.summary (delayed), transformers.models.bert_generation.modeling_bert_generation (delayed, optional), transformers.models.deprecated.realm.retrieval_realm (delayed), keras.src.callbacks.tensorboard (delayed)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named distributed - imported by fsspec.transaction (delayed), joblib._dask (optional), joblib._parallel_backends (delayed, optional)
missing module named zstandard.backend_rust - imported by zstandard (conditional)
missing module named lz4 - imported by fsspec.compression (optional), joblib.compressor (optional)
missing module named snappy - imported by fsspec.compression (delayed, optional)
missing module named lzmaffi - imported by fsspec.compression (optional)
missing module named isal - imported by fsspec.compression (optional)
missing module named 'IPython.display' - imported by llvmlite.binding.analysis (delayed, conditional, optional), tqdm.notebook (conditional, optional), rich.jupyter (delayed, optional), rich.live (delayed, conditional, optional), huggingface_hub._login (delayed, optional), gradio.blocks (delayed, conditional, optional), transformers.utils.notebook (top-level)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named ipywidgets - imported by tqdm.notebook (conditional, optional), rich.live (delayed, conditional, optional)
missing module named setuptools_scm - imported by tqdm.version (optional)
missing module named pandas.core.internals.Block - imported by pandas.core.internals (conditional), pandas.io.pytables (conditional)
missing module named numba.typed.Dict - imported by numba.typed (delayed, conditional), numba.typed.dictobject (delayed, conditional), numba.typed.dictimpl (delayed), numba.core.typing.typeof (delayed), pandas.core._numba.extensions (delayed)
missing module named 'setuptools._distutils.msvc9compiler' - imported by cffi._shimmed_dist_utils (conditional, optional)
missing module named imp - imported by cffi.verifier (conditional), cffi._imp_emulation (optional), flatbuffers.compat (conditional)
missing module named dummy_thread - imported by cffi.lock (conditional, optional), sortedcontainers.sortedlist (conditional, optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional), sortedcontainers.sortedlist (conditional, optional)
missing module named cStringIO - imported by cffi.ffiplatform (optional)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named llvmlite.ir.Constant - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed), numba.core.base (top-level), numba.core.pythonapi (top-level), numba.core.lowering (top-level), numba.core.generators (top-level), numba.core.callwrapper (top-level), numba.pycc.llvm_types (top-level), numba.cpython.unicode (top-level), numba.cpython.numbers (top-level), numba.cpython.mathimpl (top-level), numba.np.arrayobj (top-level), numba.np.npdatetime (top-level), numba.np.ufunc.wrappers (top-level), numba.np.math.mathimpl (top-level), numba.np.math.numbers (top-level)
missing module named graphviz - imported by llvmlite.binding.analysis (delayed), numba.core.ir (delayed, optional), numba.core.controlflow (delayed, optional), numba.misc.inspection (delayed, optional), numba.core.codegen (delayed), numba.core.rvsdg_frontend.rvsdg.regionrenderer (delayed)
missing module named llvmlite.ir.Value - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named llvmlite.ir.GlobalVariable - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named llvmlite.ir.Module - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named numba.core.types.StringLiteral - imported by numba.core.types (top-level), numba.np.arrayobj (top-level)
missing module named numba.core.types.Tuple - imported by numba.core.types (delayed), numba.core.types.iterators (delayed), numba.core.types.npytypes (delayed)
missing module named numba.core.types.Array - imported by numba.core.types (delayed), numba.core.types.abstract (delayed)
missing module named numba.core.types.WrapperAddressProtocol - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.FunctionPrototype - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.UndefinedFunctionType - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.FunctionType - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.ClassInstanceType - imported by numba.core.types (top-level), numba.experimental.jitclass.overloads (top-level)
missing module named numba.uint8 - imported by numba (top-level), numba.np.random.random_methods (top-level)
missing module named numba.uint16 - imported by numba (top-level), numba.np.random.random_methods (top-level)
missing module named numba.uint32 - imported by numba (top-level), numba.np.random.random_methods (top-level)
missing module named numba.uint64 - imported by numba (top-level), numba.np.random.random_methods (top-level)
missing module named numba.int64 - imported by numba (top-level), numba.np.random.distributions (top-level)
missing module named numba.float32 - imported by numba (top-level), numba.np.random.generator_core (top-level), numba.np.random.distributions (top-level)
missing module named 'elftools.elf' - imported by numba.core.codegen (delayed)
missing module named elftools - imported by numba.core.codegen (delayed)
missing module named pygments.formatters.Terminal256Formatter - imported by pygments.formatters (delayed, conditional, optional), numba.core.ir (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional), numba.core.utils (delayed, conditional, optional)
missing module named pygments.formatters.TerminalFormatter - imported by pygments.formatters (delayed, optional), numba.core.annotations.pretty_annotate (delayed, optional)
missing module named pygments.formatters.HtmlFormatter - imported by pygments.formatters (delayed, optional), numba.core.annotations.pretty_annotate (delayed, optional)
missing module named pygments.lexers.LlvmLexer - imported by pygments.lexers (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional), numba.core.utils (delayed, conditional, optional)
missing module named pygments.lexers.GasLexer - imported by pygments.lexers (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional)
missing module named pygments.lexers.PrologLexer - imported by pygments.lexers (top-level), pygments.lexers.cplint (top-level)
missing module named pygments.lexers.PythonLexer - imported by pygments.lexers (delayed, optional), numba.core.annotations.pretty_annotate (delayed, optional)
missing module named ctags - imported by pygments.formatters.html (optional)
missing module named r2pipe - imported by numba.misc.inspection (delayed, optional)
missing module named 'numba_rvsdg.core' - imported by numba.core.rvsdg_frontend.rvsdg.bc2rvsdg (top-level), numba.core.rvsdg_frontend.rvsdg.regionpasses (top-level), numba.core.rvsdg_frontend.rvsdg.regionrenderer (top-level)
missing module named 'numba_rvsdg.rendering' - imported by numba.core.rvsdg_frontend.rvsdg.bc2rvsdg (top-level)
missing module named numba_rvsdg - imported by numba.core.rvsdg_frontend (delayed, optional)
missing module named 'com.sun' - imported by numba.misc.appdirs (delayed, conditional, optional), torch._appdirs (delayed, conditional, optional)
missing module named com - imported by numba.misc.appdirs (delayed)
missing module named 'win32com.gen_py' - imported by win32com (conditional, optional)
missing module named numba.types.uint64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level)
missing module named numba.types.int64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.float64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.float32 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named ptxcompiler - imported by numba.cuda.cudadrv.driver (delayed, optional), numba.cuda.testing (delayed, optional)
missing module named cubinlinker - imported by numba.cuda.cudadrv.driver (delayed, optional), numba.cuda.testing (delayed, optional)
missing module named cuda - imported by numba.cuda.cudadrv.driver (conditional), numba.core.config (delayed, conditional, optional)
missing module named numba.types.Tuple - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.void - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.int32 - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.int16 - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named 'unittest.suite' - imported by numba.tests (top-level)
missing module named coverage - imported by numba.tests.support (optional)
missing module named xmlrunner - imported by numba.testing (delayed, conditional), torch.testing._internal.common_utils (delayed, conditional)
missing module named gitdb_speedups - imported by gitdb.fun (optional)
missing module named 'gitdb_speedups._perf' - imported by gitdb.stream (optional), gitdb.pack (optional)
missing module named sha - imported by gitdb.util (delayed, optional)
missing module named Queue - imported by numba.testing.main (optional), urllib3.util.queue (conditional)
missing module named numba.cuda.is_available - imported by numba.cuda (delayed), numba.cuda.testing (delayed)
missing module named numba.core.types.NoneType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.Type - imported by numba.core.types (top-level), numba.typed.listobject (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.ListTypeIteratorType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.ListTypeIterableType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.ListType - imported by numba.core.types (top-level), numba.typed.listobject (top-level), numba.typed.typedlist (top-level)
missing module named numba.core.types.DictIteratorType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictValuesIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictKeysIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictItemsIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictType - imported by numba.core.types (top-level), numba.typed.typeddict (top-level), numba.typed.dictobject (top-level)
missing module named numba.typed.List - imported by numba.typed (delayed), numba.core.codegen (delayed), numba.typed.listobject (delayed), numba.core.typing.typeof (delayed)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level), trio._dtls (delayed, conditional), redis.connection (delayed, conditional), google.auth.transport._mtls_helper (delayed), google.auth.transport.requests (delayed, optional), google.auth.identity_pool (delayed)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named bcrypt - imported by cryptography.hazmat.primitives.serialization.ssh (optional)
missing module named 'pyu2f.model' - imported by google.oauth2.challenges (delayed, optional)
missing module named 'pyu2f.errors' - imported by google.oauth2.challenges (delayed, optional)
missing module named pyu2f - imported by google.oauth2.challenges (delayed, optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed)
runtime module named urllib3.packages.six.moves - imported by http.client (top-level), urllib3.util.response (top-level), urllib3.connectionpool (top-level), urllib3.packages.six.moves.urllib (top-level), urllib3.util.queue (top-level)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional), httpx._decoders (optional)
missing module named urllib3_secure_extra - imported by urllib3 (optional)
missing module named 'urllib3.packages.six.moves.urllib.parse' - imported by urllib3.request (top-level), urllib3.poolmanager (top-level)
missing module named 'OpenSSL.SSL' - imported by urllib3.contrib.pyopenssl (top-level)
missing module named 'requests.packages.urllib3' - imported by google.auth.transport.requests (top-level)
missing module named simplejson - imported by requests.compat (conditional, optional), huggingface_hub.utils._fixes (optional)
missing module named win_inet_pton - imported by socks (conditional, optional)
missing module named 'google.appengine' - imported by google.auth.app_engine (optional), googleapiclient.discovery_cache.appengine_memcache (top-level)
missing module named pyarrow - imported by pandas.core.arrays.arrow.accessors (conditional), pandas.core.arrays.masked (delayed), pandas.core.arrays.boolean (delayed, conditional), pandas.core.arrays.numeric (delayed, conditional), pandas.core.arrays.arrow._arrow_utils (top-level), pandas.core.arrays.string_ (delayed, conditional), pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.arrays.interval (delayed), pandas.core.arrays.arrow.extension_types (top-level), pandas.core.arrays.period (delayed), pandas.core.interchange.utils (delayed, conditional), pandas.io.parsers.base_parser (delayed, conditional), pandas.core.methods.describe (delayed, conditional), pandas.io.sql (delayed, conditional), pandas.core.reshape.merge (delayed, conditional), pandas.core.strings.accessor (delayed, conditional), pandas.core.interchange.buffer (conditional), pandas.io.feather_format (delayed), pandas.core.indexes.base (delayed, conditional), pandas.core.dtypes.cast (delayed, conditional), pandas.core.arrays.arrow.array (conditional), pandas.core.dtypes.dtypes (delayed, conditional), pandas.compat.pyarrow (optional), pandas.core.reshape.encoding (delayed, conditional), pandas._testing (conditional)
missing module named Foundation - imported by pandas.io.clipboard (delayed, conditional, optional), pyperclip (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional), pyperclip (delayed, conditional, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named qtpy - imported by pandas.io.clipboard (delayed, conditional, optional), pyperclip (delayed, conditional, optional)
missing module named 'sqlalchemy.engine' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.types' - imported by pandas.io.sql (delayed, conditional)
missing module named 'sqlalchemy.schema' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.sql' - imported by pandas.io.sql (conditional)
missing module named sqlalchemy - imported by pandas.io.sql (delayed, conditional)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed)
missing module named 'pyarrow.compute' - imported by pandas.core.arrays.arrow.accessors (conditional), pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.reshape.merge (delayed, conditional), pandas.core.arrays.arrow.array (conditional)
missing module named xlsxwriter - imported by pandas.io.excel._xlsxwriter (delayed)
missing module named xmlrpclib - imported by defusedxml.xmlrpc (conditional)
missing module named htmlentitydefs - imported by lxml.html.soupparser (optional)
missing module named BeautifulSoup - imported by lxml.html.soupparser (optional)
missing module named cchardet - imported by bs4.dammit (optional)
missing module named bs4.builder.HTMLParserTreeBuilder - imported by bs4.builder (top-level), bs4 (top-level)
missing module named 'html5lib.treebuilders' - imported by bs4.builder._html5lib (optional), lxml.html._html5builder (top-level), lxml.html.html5parser (top-level)
missing module named 'html5lib.constants' - imported by bs4.builder._html5lib (top-level)
missing module named html5lib - imported by bs4.builder._html5lib (top-level), lxml.html.html5parser (top-level)
missing module named urlparse - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named urllib2 - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named cssselect - imported by lxml.cssselect (optional)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named xlrd - imported by pandas.io.excel._xlrd (delayed, conditional), pandas.io.excel._base (delayed, conditional)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed, conditional)
missing module named 'odf.office' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (conditional)
missing module named python_calamine - imported by pandas.io.excel._calamine (delayed, conditional)
missing module named traitlets - imported by pandas.io.formats.printing (delayed, conditional)
missing module named 'IPython.core' - imported by sympy.interactive.printing (delayed, optional), pandas.io.formats.printing (delayed, conditional), rich.pretty (delayed, optional), dotenv.ipython (top-level), gradio.utils (delayed, optional), h5py (delayed, conditional, optional), h5py.ipy_completer (top-level), gradio.ipython_ext (optional)
missing module named IPython - imported by sympy.interactive.printing (delayed, conditional, optional), sympy.interactive.session (delayed, conditional, optional), pandas.io.formats.printing (delayed), h5py (delayed, conditional, optional), h5py.ipy_completer (top-level), keras.src.utils.model_visualization (delayed, conditional, optional), keras.src.saving.file_editor (delayed, optional), tensorflow.python.keras.utils.vis_utils (delayed, conditional, optional)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by astunparse (top-level), dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level), tensorflow.python.distribute.multi_process_runner (top-level), tensorflow.python.distribute.coordinator.cluster_coordinator (top-level), six.moves.urllib (top-level), pasta.base.annotate (top-level)
missing module named six.moves.zip - imported by six.moves (top-level), pasta.base.annotate (top-level)
missing module named six.moves.cStringIO - imported by six.moves (top-level), astunparse (top-level)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named UserDict - imported by pytz.lazy (optional)
missing module named scipy.sparse.coo_matrix - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), pandas.core.arrays.sparse.accessor (delayed), scipy.io._mmio (top-level), scipy.io._fast_matrix_market (delayed, conditional), scipy.integrate._bvp (top-level), scipy.integrate._ivp.common (top-level), scipy.stats._crosstab (top-level)
missing module named scipy.stats.spearmanr - imported by scipy.stats (conditional), transformers.data.metrics (conditional), pandas.core.nanops (delayed, conditional)
missing module named scipy.special.gammaincinv - imported by scipy.special (top-level), scipy.stats._qmvnt (top-level)
missing module named scipy.special.ive - imported by scipy.special (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.betaln - imported by scipy.special (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.beta - imported by scipy.special (top-level), scipy.stats._tukeylambda_stats (top-level)
missing module named scipy.special.sph_jn - imported by scipy.special (delayed, conditional, optional), sympy.functions.special.bessel (delayed, conditional, optional)
missing module named scipy.special.loggamma - imported by scipy.special (top-level), scipy.fft._fftlog_backend (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.airy - imported by scipy.special (top-level), scipy.special._orthogonal (top-level)
missing module named cupyx - imported by scipy._lib._array_api (delayed, conditional)
missing module named jax - imported by optree.integration.jax (top-level), opt_einsum.backends.jax (delayed, conditional), keras.src.trainers.data_adapters.data_adapter_utils (delayed), transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level), safetensors.flax (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.generation.flax_logits_process (top-level), transformers.models.xglm.modeling_flax_xglm (top-level), transformers.generation.flax_utils (top-level), keras.src.backend.jax.core (top-level), keras.src.backend.jax.distribution_lib (top-level), keras.src.backend.jax.image (top-level), keras.src.backend.jax.linalg (top-level), keras.src.backend.jax.math (top-level), keras.src.backend.jax.nn (top-level), keras.src.backend.jax.random (top-level), keras.src.backend.jax.rnn (top-level), keras.src.backend.jax.trainer (top-level), keras.src.backend.numpy.image (top-level), keras.src.backend.numpy.nn (top-level), keras.src.export.export_lib (delayed), keras.src.backend.jax.optimizer (top-level), tensorflow.lite.python.util (optional), scipy._lib.array_api_compat.common._helpers (delayed), scipy._lib._array_api (delayed, conditional)
missing module named scipy.linalg.issymmetric - imported by scipy.linalg (top-level), scipy.optimize._optimize (top-level), scipy.optimize._hessian_update_strategy (top-level)
missing module named scipy.sparse.csc_matrix - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.linalg._sketches (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._trustregion_constr.qp_subproblem (top-level), scipy.optimize._linprog_highs (top-level), scipy.io._harwell_boeing.hb (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level)
missing module named scipy.sparse.diags - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.spdiags - imported by scipy.sparse (delayed), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.dia_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.eye - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level), scipy.optimize._trustregion_constr.equality_constrained_sqp (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level)
missing module named scipy.sparse.kron - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named sparse - imported by scipy.sparse.linalg._expm_multiply (delayed, conditional), scipy.sparse.linalg._matfuncs (delayed, conditional)
missing module named scipy.sparse.issparse - imported by scipy.sparse (top-level), scipy.sparse.linalg._interface (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.sparse.linalg._norm (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._constraints (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._lsq.least_squares (top-level), scipy.optimize._lsq.common (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._differentialevolution (top-level), scipy.sparse.csgraph._laplacian (top-level), scipy.optimize._milp (top-level), scipy.io._mmio (top-level), tensorflow.python.keras.engine.data_adapter (delayed, optional), tensorflow.python.keras.engine.training_arrays_v1 (optional), tensorflow.python.keras.engine.training_v1 (optional), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), pandas.core.dtypes.common (delayed, conditional, optional), scipy.sparse.csgraph._validation (top-level)
missing module named scipy.sparse.sparray - imported by scipy.sparse (delayed), scipy.sparse._index (delayed), networkx.utils.backends (delayed)
missing module named scipy.sparse.linalg.matrix_power - imported by scipy.sparse.linalg (delayed), scipy.sparse._matrix (delayed)
missing module named scikits - imported by scipy.sparse.linalg._dsolve.linsolve (optional)
missing module named scipy.sparse.SparseEfficiencyWarning - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.linalg.qr_insert - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named scipy.linalg.bandwidth - imported by scipy.linalg (top-level), scipy.linalg._matfuncs (top-level)
missing module named uarray - imported by scipy._lib.uarray (conditional, optional)
missing module named scipy.linalg._flapack_64 - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._clapack - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._fblas_64 - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._cblas - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.spatial.Voronoi - imported by scipy.spatial (top-level), scipy.stats._qmc (top-level)
missing module named scipy.interpolate.PPoly - imported by scipy.interpolate (top-level), scipy.interpolate._cubic (top-level), scipy.spatial.transform._rotation_spline (delayed), scipy.integrate._bvp (delayed)
missing module named scipy.sparse.csr_array - imported by scipy.sparse (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._ndbspline (top-level)
missing module named scipy.sparse.vstack - imported by scipy.sparse (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._milp (top-level)
missing module named scipy.sparse.csc_array - imported by scipy.sparse (top-level), scipy.optimize._milp (top-level)
missing module named scipy.sparse.find - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), scipy.integrate._ivp.common (top-level)
missing module named scipy.sparse.csr_matrix - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.sparse.csgraph._validation (top-level)
missing module named 'scikits.umfpack' - imported by scipy.optimize._linprog_ip (optional)
missing module named 'sksparse.cholmod' - imported by scipy.optimize._linprog_ip (optional)
missing module named sksparse - imported by scipy.optimize._trustregion_constr.projections (optional), scipy.optimize._linprog_ip (optional)
missing module named scipy.sparse.bmat - imported by scipy.sparse (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._trustregion_constr.qp_subproblem (top-level)
missing module named 'matplotlib.ticker' - imported by scipy.stats._fit (delayed), pandas.plotting._matplotlib.converter (top-level), pandas.plotting._matplotlib.core (delayed)
missing module named sphinx - imported by scipy._lib._docscrape (delayed, conditional)
missing module named scipy.stats.iqr - imported by scipy.stats (delayed), scipy.stats._hypotests (delayed)
missing module named scipy.stats.pearsonr - imported by scipy.stats (conditional), transformers.data.metrics (conditional)
missing module named scipy.stats.fisher_exact - imported by scipy.stats (optional), nltk.metrics.association (optional)
missing module named scipy.stats.norm - imported by scipy.stats (optional), nltk.translate.gale_church (optional)
missing module named scipy.stats.truncnorm - imported by scipy.stats (delayed, conditional), transformers.models.esm.modeling_esmfold (delayed, conditional)
missing module named pandas.core.groupby.PanelGroupBy - imported by pandas.core.groupby (delayed, optional), tqdm.std (delayed, optional)
missing module named 'matplotlib.axes' - imported by mpl_toolkits.mplot3d.axes3d (top-level), pandas.plotting._core (conditional), pandas.plotting._misc (conditional), pandas._testing.asserters (delayed), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.misc (conditional), pandas.plotting._matplotlib.timeseries (conditional), pandas.plotting._matplotlib.core (delayed, conditional), pandas.plotting._matplotlib.boxplot (conditional), pandas.plotting._matplotlib.hist (conditional)
missing module named 'matplotlib.lines' - imported by mpl_toolkits.mplot3d.axes3d (top-level), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.misc (top-level), pandas.plotting._matplotlib.boxplot (conditional)
missing module named 'matplotlib.axis' - imported by pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.converter (conditional), pandas.plotting._matplotlib.core (conditional)
missing module named 'matplotlib.artist' - imported by mpl_toolkits.mplot3d.axes3d (top-level), pandas._testing.asserters (delayed), pandas.plotting._matplotlib.boxplot (top-level), pandas.plotting._matplotlib.core (conditional)
missing module named 'matplotlib.units' - imported by pandas.plotting._matplotlib.converter (top-level)
missing module named 'matplotlib.transforms' - imported by mpl_toolkits.mplot3d.axes3d (top-level), pandas.plotting._matplotlib.converter (top-level)
missing module named 'matplotlib.dates' - imported by pandas.plotting._matplotlib.converter (top-level)
missing module named 'matplotlib.table' - imported by pandas.plotting._misc (conditional), pandas.plotting._matplotlib.tools (top-level)
missing module named pandas.core.window._Rolling_and_Expanding - imported by pandas.core.window (delayed, optional), tqdm.std (delayed, optional)
missing module named pandas.Panel - imported by pandas (delayed, optional), tqdm.std (delayed, optional)
missing module named 'pyarrow.fs' - imported by pandas.io.orc (conditional)
missing module named 'pyarrow.parquet' - imported by pandas.io.parquet (delayed)
missing module named awscrt - imported by botocore.compat (optional), botocore.useragent (delayed, optional), botocore.httpchecksum (conditional)
missing module named 'botocore.customizations' - imported by botocore.useragent (optional), botocore.configprovider (optional)
missing module named 'awscrt.io' - imported by s3transfer.crt (top-level)
missing module named 'awscrt.auth' - imported by s3transfer.crt (top-level)
missing module named 'awscrt.http' - imported by s3transfer.crt (top-level)
missing module named 'awscrt.s3' - imported by boto3.s3.transfer (conditional), s3transfer.crt (top-level)
missing module named keras.src.ops.convert_to_tensor - imported by keras.src.ops (top-level), keras.src.utils.torch_utils (top-level)
missing module named keras.src.ops.convert_to_numpy - imported by keras.src.ops (top-level), keras.src.utils.torch_utils (top-level)
missing module named annotationlib - imported by attr._compat (conditional)
missing module named linkify_it - imported by markdown_it.main (optional)
missing module named pydotplus - imported by keras.src.utils.model_visualization (optional), tensorflow.python.keras.utils.vis_utils (optional)
missing module named pydot_ng - imported by keras.src.utils.model_visualization (optional), tensorflow.python.keras.utils.vis_utils (optional)
missing module named 'flax.core' - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level), keras.src.utils.jax_layer (delayed)
missing module named _structseq - imported by optree.typing (delayed, conditional)
missing module named 'jax.typing' - imported by optree.integration.jax (top-level)
missing module named 'jax._src' - imported by optree.integration.jax (top-level)
missing module named 'jax.experimental' - imported by keras.src.trainers.data_adapters.data_adapter_utils (delayed), transformers.generation.flax_logits_process (top-level), keras.src.backend.jax.core (top-level), keras.src.backend.jax.numpy (top-level), keras.src.backend.jax.nn (top-level), keras.src.backend.jax.sparse (top-level), keras.src.export.export_lib (delayed), scipy._lib.array_api_compat.common._helpers (delayed, conditional), keras.src.testing.test_case (delayed, conditional)
missing module named 'jax.scipy' - imported by keras.src.backend.jax.linalg (top-level)
missing module named 'jax.numpy' - imported by transformers.feature_extraction_utils (delayed, conditional), transformers.image_transforms (conditional), transformers.tokenization_utils_base (delayed, conditional), transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_outputs (top-level), transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level), safetensors.flax (top-level), transformers.utils.generic (delayed, conditional), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.conditional_detr.image_processing_conditional_detr (delayed, conditional), transformers.models.deformable_detr.image_processing_deformable_detr (delayed, conditional), transformers.models.deprecated.deta.image_processing_deta (delayed, conditional), transformers.models.deprecated.jukebox.tokenization_jukebox (delayed, conditional), transformers.models.detr.image_processing_detr (delayed, conditional), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.grounding_dino.image_processing_grounding_dino (delayed, conditional), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.mt5.modeling_flax_mt5 (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.owlv2.processing_owlv2 (delayed, conditional), transformers.models.owlvit.processing_owlvit (delayed, conditional), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.rt_detr.image_processing_rt_detr (delayed, conditional), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.wav2vec2.tokenization_wav2vec2 (conditional), transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (conditional), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.generation.flax_logits_process (top-level), transformers.models.xglm.modeling_flax_xglm (top-level), transformers.models.yolos.image_processing_yolos (delayed, conditional), transformers.generation.flax_utils (top-level), keras.src.backend.jax.core (top-level), keras.src.backend.jax.image (top-level), keras.src.backend.jax.linalg (top-level), keras.src.backend.jax.math (top-level), keras.src.backend.jax.numpy (top-level), keras.src.backend.jax.nn (top-level), keras.src.backend.jax.sparse (top-level)
missing module named keras.src.backend.random - imported by keras.src.backend (top-level), keras.src.ops (top-level), keras.src.initializers.random_initializers (top-level), keras.src.testing.test_case (delayed)
missing module named eval_type_backport - imported by pydantic._internal._typing_extra (delayed, optional)
missing module named cython - imported by scipy._lib._testutils (optional), pydantic.v1.version (optional)
missing module named wmi - imported by dns.win32util (conditional)
missing module named curio - imported by sniffio._impl (delayed, conditional)
missing module named pytest - imported by scipy._lib._testutils (delayed), sympy.testing.runtests_pytest (optional), trio.testing._raises_group (conditional, optional), torch.testing._internal.common_utils (delayed, conditional, optional), h5py.tests (delayed, optional), networkx.utils.backends (delayed, conditional, optional), torch.testing._internal.optests.generate_tests (delayed, conditional), pandas._testing._io (delayed), pandas._testing (delayed), torch._numpy.testing.utils (delayed)
missing module named exceptiongroup - imported by trio._core._run (conditional), trio._highlevel_open_tcp_listeners (conditional), trio._highlevel_open_tcp_stream (conditional), trio.testing._check_streams (conditional), trio.testing._raises_group (conditional), anyio._core._exceptions (conditional), anyio._core._sockets (conditional), starlette._utils (conditional, optional), anyio._backends._asyncio (conditional), anyio._backends._trio (conditional)
missing module named _pytest - imported by trio.testing._raises_group (conditional)
missing module named hypothesis - imported by trio._core._run (delayed), torch.testing._internal.common_utils (optional), torch.testing._internal.hypothesis_utils (top-level)
missing module named tputil - imported by trio._core._concat_tb (optional)
missing module named '_pytest.outcomes' - imported by anyio._backends._asyncio (delayed)
missing module named uvloop - imported by anyio._backends._asyncio (delayed, conditional), uvicorn.loops.auto (delayed, optional), uvicorn.loops.uvloop (top-level)
missing module named socksio - imported by httpcore._sync.socks_proxy (top-level), httpcore._async.socks_proxy (top-level), httpx._transports.default (delayed, conditional, optional)
missing module named 'h2.settings' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.exceptions' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.events' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.connection' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named h2 - imported by httpcore._sync.http2 (top-level), httpx._client (delayed, conditional, optional)
missing module named 'h2.config' - imported by httpcore._async.http2 (top-level)
missing module named '_typeshed.wsgi' - imported by httpx._transports.wsgi (conditional), werkzeug._internal (conditional), werkzeug.exceptions (conditional), werkzeug.http (conditional), werkzeug.wsgi (conditional), werkzeug.utils (conditional), werkzeug.wrappers.response (conditional), werkzeug.test (conditional), werkzeug.formparser (conditional), werkzeug.wrappers.request (conditional), werkzeug.serving (conditional), werkzeug.debug (conditional), werkzeug.middleware.shared_data (conditional), werkzeug.routing.exceptions (conditional), werkzeug.routing.map (conditional)
missing module named 'aioquic.quic' - imported by dns.quic._asyncio (top-level), dns.quic._common (top-level), dns.quic._sync (top-level), dns.quic._trio (top-level)
missing module named 'aioquic.h3' - imported by dns.quic._common (top-level)
missing module named aioquic - imported by dns.quic (conditional)
missing module named pydantic.PydanticUserError - imported by pydantic (top-level), pydantic.root_model (top-level)
missing module named pydantic.PydanticSchemaGenerationError - imported by pydantic (delayed), pydantic.functional_validators (delayed, conditional), fastapi._compat (conditional)
missing module named pydantic.BaseModel - imported by pydantic (conditional), pydantic._internal._typing_extra (conditional), pydantic._internal._import_utils (delayed, conditional), pydantic._internal._core_utils (delayed), pydantic.deprecated.copy_internals (delayed, conditional), fastapi.exceptions (top-level), fastapi.types (top-level), fastapi._compat (top-level), fastapi.openapi.models (top-level), fastapi.security.http (top-level), fastapi.utils (top-level), fastapi.dependencies.utils (top-level), fastapi.encoders (top-level), fastapi.routing (top-level), fastapi.openapi.utils (top-level), huggingface_hub._webhooks_payload (conditional), gradio.data_classes (top-level), gradio.server_messages (top-level), openai.resources.beta.realtime.realtime (top-level)
missing module named 'google.colab' - imported by huggingface_hub.utils._auth (delayed, optional)
missing module named 'torch_xla.core' - imported by accelerate.utils.imports (conditional, optional), accelerate.optimizer (conditional), accelerate.state (conditional), accelerate.utils.operations (conditional), accelerate.utils.other (conditional), accelerate.utils.random (conditional), accelerate.checkpointing (conditional), accelerate.accelerator (conditional), transformers.trainer_pt_utils (delayed, conditional), transformers.training_args (conditional), transformers.trainer (conditional), huggingface_hub.serialization._torch (delayed, conditional, optional), torch._dynamo.testing (delayed, conditional), torch._dynamo.backends.torchxla (delayed, optional)
missing module named torch_xla - imported by transformers.utils.import_utils (delayed), transformers.pytorch_utils (delayed, conditional), transformers.trainer (conditional), huggingface_hub.serialization._torch (delayed, conditional)
missing module named hf_transfer - imported by huggingface_hub.file_download (delayed, conditional, optional), huggingface_hub.lfs (delayed, optional)
missing module named hf_xet - imported by huggingface_hub.file_download (delayed, optional), huggingface_hub._commit_api (delayed)
missing module named tf_keras - imported by tensorflow.python.util.lazy_loader (delayed, conditional, optional), transformers.activations_tf (optional), transformers.modeling_tf_utils (optional), huggingface_hub.keras_mixin (conditional, optional), tensorflow.python.saved_model.load (delayed, conditional, optional)
missing module named 'mcp.client' - imported by huggingface_hub.inference._mcp.mcp_client (delayed, conditional)
missing module named mcp - imported by gradio.mcp (top-level), huggingface_hub.inference._mcp.utils (conditional), huggingface_hub.inference._mcp.mcp_client (delayed, conditional)
missing module named aiohttp - imported by huggingface_hub.inference._common (delayed, conditional), huggingface_hub.inference._generated._async_client (conditional)
missing module named fastai - imported by huggingface_hub.fastai_utils (delayed)
missing module named 'twine.settings' - imported by gradio.cli.commands.components.publish (delayed, conditional, optional)
missing module named 'twine.exceptions' - imported by gradio.cli.commands.components.publish (delayed, conditional, optional)
missing module named twine - imported by gradio.cli.commands.components.publish (delayed, conditional, optional)
missing module named 'watchdog.observers' - imported by werkzeug._reloader (delayed)
missing module named 'watchdog.events' - imported by werkzeug._reloader (delayed)
missing module named watchdog - imported by werkzeug._reloader (delayed)
missing module named 'python_socks.sync' - imported by websockets.sync.client (optional)
missing module named python_socks - imported by websockets.asyncio.client (optional), websockets.sync.client (optional)
missing module named 'python_socks.async_' - imported by websockets.asyncio.client (optional)
missing module named pyctcdecode - imported by transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (delayed, conditional), transformers.pipelines.automatic_speech_recognition (conditional), transformers.pipelines (delayed, conditional, optional)
missing module named kenlm - imported by transformers.pipelines (delayed, conditional, optional)
missing module named mlx - imported by transformers.tokenization_utils_base (delayed, conditional)
missing module named bitsandbytes - imported by transformers.utils.import_utils (delayed), accelerate.utils.imports (delayed), accelerate.utils.bnb (delayed), transformers.models.rwkv.modeling_rwkv (delayed), transformers.quantizers.quantizer_bnb_4bit (delayed), transformers.quantizers.quantizer_bnb_8bit (delayed), transformers.modeling_utils (delayed, conditional), transformers.integrations.bitsandbytes (delayed, conditional), transformers.trainer (delayed, conditional)
missing module named intel_extension_for_pytorch - imported by transformers.utils.import_utils (delayed, conditional), accelerate.utils.imports (delayed, conditional), accelerate.accelerator (delayed, conditional), transformers.trainer (delayed)
missing module named 'habana_frameworks.torch' - imported by transformers.utils.import_utils (delayed), accelerate.utils.imports (delayed, conditional)
missing module named habana_frameworks - imported by transformers.utils.import_utils (delayed, conditional)
missing module named torch_musa - imported by transformers.utils.import_utils (delayed), accelerate.utils.imports (delayed), accelerate.state (conditional), accelerate.utils.modeling (conditional)
missing module named torch_mlu - imported by transformers.utils.import_utils (delayed), accelerate.utils.imports (delayed), accelerate.state (conditional), accelerate.utils.modeling (conditional)
missing module named torch_npu - imported by transformers.utils.import_utils (delayed), accelerate.utils.imports (delayed), accelerate.state (conditional), accelerate.utils.modeling (conditional), accelerate.accelerator (conditional), transformers.modeling_flash_attention_utils (conditional), transformers.integrations.npu_flash_attention (conditional)
missing module named mamba_ssm - imported by transformers.utils.import_utils (delayed, conditional), transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (top-level)
missing module named gguf - imported by transformers.modeling_gguf_pytorch_utils (delayed, conditional)
missing module named 'optimum.quanto' - imported by transformers.cache_utils (delayed, conditional), transformers.quantizers.quantizer_quanto (delayed, conditional), transformers.integrations.quanto (delayed, conditional)
missing module named optimum - imported by transformers.cache_utils (delayed, conditional)
missing module named hqq - imported by transformers.cache_utils (conditional)
missing module named vptq - imported by transformers.quantizers.quantizer_vptq (delayed, conditional), transformers.integrations.vptq (top-level)
missing module named megatron - imported by accelerate.utils.dataclasses (delayed)
missing module named torch.distributed.fsdp.OffloadPolicy - imported by torch.distributed.fsdp (delayed, conditional), accelerate.utils.dataclasses (delayed, conditional)
missing module named torch.distributed.fsdp.CPUOffloadPolicy - imported by torch.distributed.fsdp (delayed, conditional), accelerate.utils.dataclasses (delayed, conditional)
missing module named torch.distributed.fsdp.MixedPrecisionPolicy - imported by torch.distributed.fsdp (delayed, conditional), accelerate.utils.dataclasses (delayed, conditional), accelerate.utils.fsdp_utils (delayed)
missing module named 'deepspeed.utils' - imported by accelerate.utils.dataclasses (delayed, conditional), transformers.integrations.deepspeed (delayed)
missing module named 'transformers.deepspeed' - imported by accelerate.utils.dataclasses (delayed, conditional)
missing module named 'deepspeed.ops' - imported by accelerate.utils.deepspeed (delayed, conditional)
missing module named 'bitsandbytes.optim' - imported by accelerate.utils.deepspeed (delayed, conditional), transformers.trainer (delayed, conditional, optional)
missing module named deepspeed - imported by accelerate.utils.deepspeed (delayed), accelerate.state (delayed, conditional), accelerate.utils.operations (delayed, conditional), accelerate.utils.other (delayed, conditional), accelerate.accelerator (delayed), transformers.integrations.deepspeed (delayed, conditional), transformers.models.distilbert.modeling_distilbert (delayed, conditional), transformers.models.esm.modeling_esmfold (delayed, conditional, optional), transformers.models.fsmt.modeling_fsmt (delayed, conditional), transformers.models.hubert.modeling_hubert (delayed, conditional), transformers.models.seamless_m4t.modeling_seamless_m4t (delayed, conditional), transformers.models.sew.modeling_sew (delayed, conditional), transformers.models.sew_d.modeling_sew_d (delayed, conditional), transformers.models.speecht5.modeling_speecht5 (delayed, conditional), transformers.models.unispeech.modeling_unispeech (delayed, conditional), transformers.models.unispeech_sat.modeling_unispeech_sat (delayed, conditional), transformers.models.wav2vec2.modeling_wav2vec2 (delayed, conditional), transformers.models.wav2vec2_conformer.modeling_wav2vec2_conformer (delayed, conditional), transformers.models.wavlm.modeling_wavlm (delayed, conditional), transformers.modeling_utils (delayed, conditional)
missing module named lomo_optim - imported by accelerate.optimizer (delayed, conditional), accelerate.accelerator (delayed, conditional), transformers.trainer (delayed, conditional)
missing module named torchao - imported by accelerate.utils.dataclasses (conditional), transformers.quantizers.quantizer_torchao (delayed, conditional)
missing module named torch_sdaa - imported by accelerate.utils.imports (delayed), accelerate.state (conditional), accelerate.utils.modeling (conditional)
missing module named 'torch_xla.runtime' - imported by accelerate.utils.imports (conditional, optional), accelerate.optimizer (conditional), accelerate.state (conditional), transformers.trainer_utils (delayed, conditional), transformers.trainer_pt_utils (conditional), transformers.trainer (conditional)
missing module named pynvml - imported by torch.cuda (delayed, conditional, optional), torch.cuda.memory (delayed, conditional, optional), accelerate.utils.environment (delayed, conditional)
missing module named 'transformer_engine.common' - imported by accelerate.utils.transformer_engine (delayed, conditional)
missing module named 'intel_transformer_engine.recipe' - imported by accelerate.utils.transformer_engine (delayed, conditional)
missing module named 'transformer_engine.pytorch' - imported by accelerate.utils.transformer_engine (delayed, conditional)
missing module named transformer_engine - imported by accelerate.utils.transformer_engine (delayed, conditional)
missing module named intel_transformer_engine - imported by accelerate.utils.transformer_engine (delayed, conditional)
missing module named 'intel_extension_for_pytorch.xpu' - imported by accelerate.utils.memory (delayed, conditional)
missing module named pretrain_t5 - imported by accelerate.utils.megatron_lm (delayed, conditional, optional)
missing module named pretrain_gpt - imported by accelerate.utils.megatron_lm (delayed, conditional, optional)
missing module named pretrain_bert - imported by accelerate.utils.megatron_lm (delayed, conditional, optional)
missing module named 'megatron.training' - imported by accelerate.utils.megatron_lm (conditional)
missing module named 'megatron.legacy' - imported by accelerate.utils.megatron_lm (conditional)
missing module named 'megatron.inference' - imported by accelerate.utils.megatron_lm (conditional)
missing module named 'megatron.core' - imported by accelerate.utils.megatron_lm (conditional)
missing module named torch.nn.MSELoss - imported by torch.nn (top-level), accelerate.utils.megatron_lm (top-level), transformers.models.roformer.modeling_roformer (top-level), transformers.models.albert.modeling_albert (top-level), transformers.models.audio_spectrogram_transformer.modeling_audio_spectrogram_transformer (top-level), transformers.models.bart.modeling_bart (top-level), transformers.models.beit.modeling_beit (top-level), transformers.models.bert.modeling_bert (top-level), transformers.models.big_bird.modeling_big_bird (top-level), transformers.models.bigbird_pegasus.modeling_bigbird_pegasus (top-level), transformers.models.biogpt.modeling_biogpt (top-level), transformers.models.bit.modeling_bit (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.camembert.modeling_camembert (top-level), transformers.models.canine.modeling_canine (top-level), transformers.models.clip.modeling_clip (top-level), transformers.models.convbert.modeling_convbert (top-level), transformers.models.convnext.modeling_convnext (top-level), transformers.models.convnextv2.modeling_convnextv2 (top-level), transformers.models.ctrl.modeling_ctrl (top-level), transformers.models.cvt.modeling_cvt (top-level), transformers.models.data2vec.modeling_data2vec_text (top-level), transformers.models.data2vec.modeling_data2vec_vision (top-level), transformers.models.deberta.modeling_deberta (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.deit.modeling_deit (top-level), transformers.models.deprecated.efficientformer.modeling_efficientformer (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), transformers.models.deprecated.graphormer.modeling_graphormer (top-level), transformers.models.deprecated.mega.modeling_mega (top-level), transformers.models.deprecated.mmbt.modeling_mmbt (top-level), transformers.models.deprecated.nat.modeling_nat (top-level), transformers.models.deprecated.nezha.modeling_nezha (top-level), transformers.models.deprecated.open_llama.modeling_open_llama (top-level), transformers.models.deprecated.qdqbert.modeling_qdqbert (top-level), transformers.models.deprecated.transfo_xl.modeling_transfo_xl (top-level), transformers.models.deprecated.tvlt.modeling_tvlt (top-level), transformers.models.deprecated.van.modeling_van (top-level), transformers.models.deprecated.vit_hybrid.modeling_vit_hybrid (top-level), transformers.models.dinat.modeling_dinat (top-level), transformers.models.dinov2.modeling_dinov2 (top-level), transformers.models.dinov2_with_registers.modeling_dinov2_with_registers (top-level), transformers.models.distilbert.modeling_distilbert (top-level), transformers.models.efficientnet.modeling_efficientnet (top-level), transformers.models.electra.modeling_electra (top-level), transformers.models.ernie.modeling_ernie (top-level), transformers.models.esm.modeling_esm (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.flaubert.modeling_flaubert (top-level), transformers.models.fnet.modeling_fnet (top-level), transformers.models.focalnet.modeling_focalnet (top-level), transformers.models.funnel.modeling_funnel (top-level), transformers.models.siglip.modeling_siglip (top-level), transformers.models.gpt2.modeling_gpt2 (top-level), transformers.models.gpt_bigcode.modeling_gpt_bigcode (top-level), transformers.models.gpt_neo.modeling_gpt_neo (top-level), transformers.models.gptj.modeling_gptj (top-level), transformers.models.hgnet_v2.modeling_hgnet_v2 (top-level), transformers.models.hiera.modeling_hiera (top-level), transformers.models.ibert.modeling_ibert (top-level), transformers.models.ijepa.modeling_ijepa (top-level), transformers.models.imagegpt.modeling_imagegpt (top-level), transformers.models.layoutlm.modeling_layoutlm (top-level), transformers.models.layoutlmv2.modeling_layoutlmv2 (top-level), transformers.models.layoutlmv3.modeling_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_xlm_roberta (top-level), transformers.models.led.modeling_led (top-level), transformers.models.levit.modeling_levit (top-level), transformers.models.lilt.modeling_lilt (top-level), transformers.models.longformer.modeling_longformer (top-level), transformers.models.luke.modeling_luke (top-level), transformers.models.markuplm.modeling_markuplm (top-level), transformers.models.mbart.modeling_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (top-level), transformers.models.mobilebert.modeling_mobilebert (top-level), transformers.models.mobilenet_v1.modeling_mobilenet_v1 (top-level), transformers.models.mobilenet_v2.modeling_mobilenet_v2 (top-level), transformers.models.mobilevit.modeling_mobilevit (top-level), transformers.models.mobilevitv2.modeling_mobilevitv2 (top-level), transformers.models.modernbert.modeling_modernbert (top-level), transformers.models.mpnet.modeling_mpnet (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.mra.modeling_mra (top-level), transformers.models.t5.modeling_t5 (top-level), transformers.models.mt5.modeling_mt5 (top-level), transformers.models.mvp.modeling_mvp (top-level), transformers.models.nystromformer.modeling_nystromformer (top-level), transformers.models.openai.modeling_openai (top-level), transformers.models.opt.modeling_opt (top-level), transformers.models.perceiver.modeling_perceiver (top-level), transformers.models.plbart.modeling_plbart (top-level), transformers.models.poolformer.modeling_poolformer (top-level), transformers.models.pvt.modeling_pvt (top-level), transformers.models.pvt_v2.modeling_pvt_v2 (top-level), transformers.models.reformer.modeling_reformer (top-level), transformers.models.regnet.modeling_regnet (top-level), transformers.models.rembert.modeling_rembert (top-level), transformers.models.resnet.modeling_resnet (top-level), transformers.models.roberta.modeling_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (top-level), transformers.models.segformer.modeling_segformer (top-level), transformers.models.siglip2.modeling_siglip2 (top-level), transformers.models.squeezebert.modeling_squeezebert (top-level), transformers.models.swiftformer.modeling_swiftformer (top-level), transformers.models.tapas.modeling_tapas (top-level), transformers.models.textnet.modeling_textnet (top-level), transformers.models.timesformer.modeling_timesformer (top-level), transformers.models.timm_wrapper.modeling_timm_wrapper (top-level), transformers.models.umt5.modeling_umt5 (top-level), transformers.models.videomae.modeling_videomae (top-level), transformers.models.vit.modeling_vit (top-level), transformers.models.vit_msn.modeling_vit_msn (top-level), transformers.models.vivit.modeling_vivit (top-level), transformers.models.xlm.modeling_xlm (top-level), transformers.models.xlm_roberta_xl.modeling_xlm_roberta_xl (top-level), transformers.models.xlnet.modeling_xlnet (top-level), transformers.models.xmod.modeling_xmod (top-level), transformers.models.yoso.modeling_yoso (top-level), transformers.models.zamba.modeling_zamba (top-level), transformers.models.zamba2.modeling_zamba2 (top-level), transformers.loss.loss_utils (top-level)
missing module named torch.nn.CrossEntropyLoss - imported by torch.nn (top-level), accelerate.utils.megatron_lm (top-level), transformers.modeling_utils (top-level), transformers.models.encoder_decoder.modeling_encoder_decoder (top-level), transformers.models.roformer.modeling_roformer (top-level), transformers.models.albert.modeling_albert (top-level), transformers.models.audio_spectrogram_transformer.modeling_audio_spectrogram_transformer (top-level), transformers.models.bart.modeling_bart (top-level), transformers.models.beit.modeling_beit (top-level), transformers.models.bert.modeling_bert (top-level), transformers.models.big_bird.modeling_big_bird (top-level), transformers.models.bigbird_pegasus.modeling_bigbird_pegasus (top-level), transformers.models.biogpt.modeling_biogpt (top-level), transformers.models.bit.modeling_bit (top-level), transformers.models.blenderbot.modeling_blenderbot (top-level), transformers.models.blenderbot_small.modeling_blenderbot_small (top-level), transformers.models.blip.modeling_blip_text (top-level), transformers.models.blip_2.modeling_blip_2 (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.bridgetower.modeling_bridgetower (top-level), transformers.models.bros.modeling_bros (top-level), transformers.models.camembert.modeling_camembert (top-level), transformers.models.canine.modeling_canine (top-level), transformers.models.clip.modeling_clip (top-level), transformers.models.clvp.modeling_clvp (top-level), transformers.models.convbert.modeling_convbert (top-level), transformers.models.convnext.modeling_convnext (top-level), transformers.models.convnextv2.modeling_convnextv2 (top-level), transformers.models.cpmant.modeling_cpmant (top-level), transformers.models.ctrl.modeling_ctrl (top-level), transformers.models.cvt.modeling_cvt (top-level), transformers.models.data2vec.modeling_data2vec_audio (top-level), transformers.models.data2vec.modeling_data2vec_text (top-level), transformers.models.data2vec.modeling_data2vec_vision (top-level), transformers.models.deberta.modeling_deberta (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.deit.modeling_deit (top-level), transformers.models.deprecated.efficientformer.modeling_efficientformer (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), transformers.models.deprecated.graphormer.modeling_graphormer (top-level), transformers.models.deprecated.mega.modeling_mega (top-level), transformers.models.deprecated.mmbt.modeling_mmbt (top-level), transformers.models.deprecated.nat.modeling_nat (top-level), transformers.models.deprecated.nezha.modeling_nezha (top-level), transformers.models.deprecated.open_llama.modeling_open_llama (top-level), transformers.models.deprecated.qdqbert.modeling_qdqbert (top-level), transformers.models.deprecated.realm.modeling_realm (top-level), transformers.models.deprecated.speech_to_text_2.modeling_speech_to_text_2 (top-level), transformers.models.deprecated.transfo_xl.modeling_transfo_xl (top-level), transformers.models.deprecated.tvlt.modeling_tvlt (top-level), transformers.models.deprecated.van.modeling_van (top-level), transformers.models.deprecated.vit_hybrid.modeling_vit_hybrid (top-level), transformers.models.dinat.modeling_dinat (top-level), transformers.models.dinov2.modeling_dinov2 (top-level), transformers.models.dinov2_with_registers.modeling_dinov2_with_registers (top-level), transformers.models.distilbert.modeling_distilbert (top-level), transformers.models.dpt.modeling_dpt (top-level), transformers.models.efficientnet.modeling_efficientnet (top-level), transformers.models.electra.modeling_electra (top-level), transformers.models.ernie.modeling_ernie (top-level), transformers.models.esm.modeling_esm (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.falcon_mamba.modeling_falcon_mamba (top-level), transformers.models.flaubert.modeling_flaubert (top-level), transformers.models.fnet.modeling_fnet (top-level), transformers.models.focalnet.modeling_focalnet (top-level), transformers.models.fsmt.modeling_fsmt (top-level), transformers.models.funnel.modeling_funnel (top-level), transformers.models.siglip.modeling_siglip (top-level), transformers.models.gpt2.modeling_gpt2 (top-level), transformers.models.gpt_bigcode.modeling_gpt_bigcode (top-level), transformers.models.gpt_neo.modeling_gpt_neo (top-level), transformers.models.gptj.modeling_gptj (top-level), transformers.models.hgnet_v2.modeling_hgnet_v2 (top-level), transformers.models.hiera.modeling_hiera (top-level), transformers.models.hubert.modeling_hubert (top-level), transformers.models.ibert.modeling_ibert (top-level), transformers.models.ijepa.modeling_ijepa (top-level), transformers.models.imagegpt.modeling_imagegpt (top-level), transformers.models.layoutlm.modeling_layoutlm (top-level), transformers.models.layoutlmv2.modeling_layoutlmv2 (top-level), transformers.models.layoutlmv3.modeling_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_xlm_roberta (top-level), transformers.models.led.modeling_led (top-level), transformers.models.levit.modeling_levit (top-level), transformers.models.lilt.modeling_lilt (top-level), transformers.models.longformer.modeling_longformer (top-level), transformers.models.longt5.modeling_longt5 (top-level), transformers.models.luke.modeling_luke (top-level), transformers.models.lxmert.modeling_lxmert (top-level), transformers.models.m2m_100.modeling_m2m_100 (top-level), transformers.models.mamba.modeling_mamba (top-level), transformers.models.marian.modeling_marian (top-level), transformers.models.markuplm.modeling_markuplm (top-level), transformers.models.mbart.modeling_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (top-level), transformers.models.mobilebert.modeling_mobilebert (top-level), transformers.models.mobilenet_v1.modeling_mobilenet_v1 (top-level), transformers.models.mobilenet_v2.modeling_mobilenet_v2 (top-level), transformers.models.mobilevit.modeling_mobilevit (top-level), transformers.models.mobilevitv2.modeling_mobilevitv2 (top-level), transformers.models.modernbert.modeling_modernbert (top-level), transformers.models.moshi.modeling_moshi (top-level), transformers.models.mpnet.modeling_mpnet (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.mra.modeling_mra (top-level), transformers.models.t5.modeling_t5 (top-level), transformers.models.mt5.modeling_mt5 (top-level), transformers.models.musicgen.modeling_musicgen (top-level), transformers.models.musicgen_melody.modeling_musicgen_melody (top-level), transformers.models.mvp.modeling_mvp (top-level), transformers.models.nllb_moe.modeling_nllb_moe (top-level), transformers.models.nystromformer.modeling_nystromformer (top-level), transformers.models.openai.modeling_openai (top-level), transformers.models.opt.modeling_opt (top-level), transformers.models.pegasus.modeling_pegasus (top-level), transformers.models.pegasus_x.modeling_pegasus_x (top-level), transformers.models.perceiver.modeling_perceiver (top-level), transformers.models.plbart.modeling_plbart (top-level), transformers.models.poolformer.modeling_poolformer (top-level), transformers.models.pop2piano.modeling_pop2piano (top-level), transformers.models.pvt.modeling_pvt (top-level), transformers.models.pvt_v2.modeling_pvt_v2 (top-level), transformers.models.reformer.modeling_reformer (top-level), transformers.models.regnet.modeling_regnet (top-level), transformers.models.rembert.modeling_rembert (top-level), transformers.models.resnet.modeling_resnet (top-level), transformers.models.roberta.modeling_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (top-level), transformers.models.seamless_m4t.modeling_seamless_m4t (top-level), transformers.models.seamless_m4t_v2.modeling_seamless_m4t_v2 (top-level), transformers.models.segformer.modeling_segformer (top-level), transformers.models.sew.modeling_sew (top-level), transformers.models.sew_d.modeling_sew_d (top-level), transformers.models.siglip2.modeling_siglip2 (top-level), transformers.models.speech_encoder_decoder.modeling_speech_encoder_decoder (top-level), transformers.models.speech_to_text.modeling_speech_to_text (top-level), transformers.models.speecht5.modeling_speecht5 (top-level), transformers.models.splinter.modeling_splinter (top-level), transformers.models.squeezebert.modeling_squeezebert (top-level), transformers.models.swiftformer.modeling_swiftformer (top-level), transformers.models.switch_transformers.modeling_switch_transformers (top-level), transformers.models.tapas.modeling_tapas (top-level), transformers.models.textnet.modeling_textnet (top-level), transformers.models.timesformer.modeling_timesformer (top-level), transformers.models.timm_wrapper.modeling_timm_wrapper (top-level), transformers.models.trocr.modeling_trocr (top-level), transformers.models.udop.modeling_udop (top-level), transformers.models.umt5.modeling_umt5 (top-level), transformers.models.unispeech.modeling_unispeech (top-level), transformers.models.unispeech_sat.modeling_unispeech_sat (top-level), transformers.models.upernet.modeling_upernet (top-level), transformers.models.videomae.modeling_videomae (top-level), transformers.models.vilt.modeling_vilt (top-level), transformers.models.visual_bert.modeling_visual_bert (top-level), transformers.models.vit.modeling_vit (top-level), transformers.models.vit_msn.modeling_vit_msn (top-level), transformers.models.vivit.modeling_vivit (top-level), transformers.models.wav2vec2.modeling_wav2vec2 (top-level), transformers.models.wav2vec2_bert.modeling_wav2vec2_bert (top-level), transformers.models.wav2vec2_conformer.modeling_wav2vec2_conformer (top-level), transformers.models.wavlm.modeling_wavlm (top-level), transformers.models.whisper.modeling_whisper (top-level), transformers.models.xlm.modeling_xlm (top-level), transformers.models.xlm_roberta_xl.modeling_xlm_roberta_xl (top-level), transformers.models.xlnet.modeling_xlnet (top-level), transformers.models.xmod.modeling_xmod (top-level), transformers.models.yoso.modeling_yoso (top-level), transformers.models.zamba.modeling_zamba (top-level), transformers.models.zamba2.modeling_zamba2 (top-level)
missing module named torch.nn.BCEWithLogitsLoss - imported by torch.nn (top-level), accelerate.utils.megatron_lm (top-level), transformers.models.roformer.modeling_roformer (top-level), transformers.models.albert.modeling_albert (top-level), transformers.models.audio_spectrogram_transformer.modeling_audio_spectrogram_transformer (top-level), transformers.models.bart.modeling_bart (top-level), transformers.models.beit.modeling_beit (top-level), transformers.models.bert.modeling_bert (top-level), transformers.models.big_bird.modeling_big_bird (top-level), transformers.models.bigbird_pegasus.modeling_bigbird_pegasus (top-level), transformers.models.biogpt.modeling_biogpt (top-level), transformers.models.bit.modeling_bit (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.camembert.modeling_camembert (top-level), transformers.models.canine.modeling_canine (top-level), transformers.models.clip.modeling_clip (top-level), transformers.models.convbert.modeling_convbert (top-level), transformers.models.convnext.modeling_convnext (top-level), transformers.models.convnextv2.modeling_convnextv2 (top-level), transformers.models.ctrl.modeling_ctrl (top-level), transformers.models.cvt.modeling_cvt (top-level), transformers.models.data2vec.modeling_data2vec_text (top-level), transformers.models.data2vec.modeling_data2vec_vision (top-level), transformers.models.deberta.modeling_deberta (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.deit.modeling_deit (top-level), transformers.models.deprecated.efficientformer.modeling_efficientformer (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), transformers.models.deprecated.graphormer.modeling_graphormer (top-level), transformers.models.deprecated.mega.modeling_mega (top-level), transformers.models.deprecated.nat.modeling_nat (top-level), transformers.models.deprecated.nezha.modeling_nezha (top-level), transformers.models.deprecated.open_llama.modeling_open_llama (top-level), transformers.models.deprecated.qdqbert.modeling_qdqbert (top-level), transformers.models.deprecated.transfo_xl.modeling_transfo_xl (top-level), transformers.models.deprecated.tvlt.modeling_tvlt (top-level), transformers.models.deprecated.van.modeling_van (top-level), transformers.models.deprecated.vit_hybrid.modeling_vit_hybrid (top-level), transformers.models.dinat.modeling_dinat (top-level), transformers.models.dinov2.modeling_dinov2 (top-level), transformers.models.dinov2_with_registers.modeling_dinov2_with_registers (top-level), transformers.models.distilbert.modeling_distilbert (top-level), transformers.models.efficientnet.modeling_efficientnet (top-level), transformers.models.electra.modeling_electra (top-level), transformers.models.ernie.modeling_ernie (top-level), transformers.models.esm.modeling_esm (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.flaubert.modeling_flaubert (top-level), transformers.models.fnet.modeling_fnet (top-level), transformers.models.focalnet.modeling_focalnet (top-level), transformers.models.funnel.modeling_funnel (top-level), transformers.models.siglip.modeling_siglip (top-level), transformers.models.gpt2.modeling_gpt2 (top-level), transformers.models.gpt_bigcode.modeling_gpt_bigcode (top-level), transformers.models.gpt_neo.modeling_gpt_neo (top-level), transformers.models.gptj.modeling_gptj (top-level), transformers.models.hgnet_v2.modeling_hgnet_v2 (top-level), transformers.models.hiera.modeling_hiera (top-level), transformers.models.ibert.modeling_ibert (top-level), transformers.models.ijepa.modeling_ijepa (top-level), transformers.models.imagegpt.modeling_imagegpt (top-level), transformers.models.layoutlm.modeling_layoutlm (top-level), transformers.models.layoutlmv2.modeling_layoutlmv2 (top-level), transformers.models.layoutlmv3.modeling_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_xlm_roberta (top-level), transformers.models.led.modeling_led (top-level), transformers.models.levit.modeling_levit (top-level), transformers.models.lilt.modeling_lilt (top-level), transformers.models.longformer.modeling_longformer (top-level), transformers.models.luke.modeling_luke (top-level), transformers.models.markuplm.modeling_markuplm (top-level), transformers.models.mbart.modeling_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (top-level), transformers.models.mobilebert.modeling_mobilebert (top-level), transformers.models.mobilenet_v1.modeling_mobilenet_v1 (top-level), transformers.models.mobilenet_v2.modeling_mobilenet_v2 (top-level), transformers.models.mobilevit.modeling_mobilevit (top-level), transformers.models.mobilevitv2.modeling_mobilevitv2 (top-level), transformers.models.modernbert.modeling_modernbert (top-level), transformers.models.mpnet.modeling_mpnet (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.mra.modeling_mra (top-level), transformers.models.t5.modeling_t5 (top-level), transformers.models.mt5.modeling_mt5 (top-level), transformers.models.mvp.modeling_mvp (top-level), transformers.models.nystromformer.modeling_nystromformer (top-level), transformers.models.openai.modeling_openai (top-level), transformers.models.opt.modeling_opt (top-level), transformers.models.perceiver.modeling_perceiver (top-level), transformers.models.plbart.modeling_plbart (top-level), transformers.models.poolformer.modeling_poolformer (top-level), transformers.models.pvt.modeling_pvt (top-level), transformers.models.pvt_v2.modeling_pvt_v2 (top-level), transformers.models.reformer.modeling_reformer (top-level), transformers.models.regnet.modeling_regnet (top-level), transformers.models.rembert.modeling_rembert (top-level), transformers.models.resnet.modeling_resnet (top-level), transformers.models.roberta.modeling_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (top-level), transformers.models.segformer.modeling_segformer (top-level), transformers.models.siglip2.modeling_siglip2 (top-level), transformers.models.speecht5.modeling_speecht5 (top-level), transformers.models.squeezebert.modeling_squeezebert (top-level), transformers.models.swiftformer.modeling_swiftformer (top-level), transformers.models.tapas.modeling_tapas (top-level), transformers.models.textnet.modeling_textnet (top-level), transformers.models.timesformer.modeling_timesformer (top-level), transformers.models.timm_wrapper.modeling_timm_wrapper (top-level), transformers.models.umt5.modeling_umt5 (top-level), transformers.models.videomae.modeling_videomae (top-level), transformers.models.vit.modeling_vit (top-level), transformers.models.vit_msn.modeling_vit_msn (top-level), transformers.models.xlm.modeling_xlm (top-level), transformers.models.xlm_roberta_xl.modeling_xlm_roberta_xl (top-level), transformers.models.xlnet.modeling_xlnet (top-level), transformers.models.xmod.modeling_xmod (top-level), transformers.models.yoso.modeling_yoso (top-level), transformers.models.zamba.modeling_zamba (top-level), transformers.models.zamba2.modeling_zamba2 (top-level), transformers.loss.loss_utils (top-level)
missing module named torch.distributed.fsdp.fully_shard - imported by torch.distributed.fsdp (delayed), accelerate.utils.fsdp_utils (delayed)
missing module named torch.distributed.fsdp.FSDPModule - imported by torch.distributed.fsdp (delayed), accelerate.utils.fsdp_utils (delayed)
missing module named peft - imported by accelerate.utils.modeling (delayed, conditional), accelerate.utils.fsdp_utils (delayed, conditional), transformers.trainer (delayed, conditional), transformers.integrations.peft (delayed)
missing module named 'torch_xla.amp' - imported by accelerate.utils.modeling (delayed, conditional), transformers.trainer (delayed, conditional, optional)
missing module named 'torchao.float8' - imported by accelerate.utils.ao (delayed, conditional)
missing module named torch_ccl - imported by accelerate.state (delayed, conditional)
missing module named oneccl_bindings_for_pytorch - imported by accelerate.state (delayed, conditional)
missing module named smdistributed - imported by accelerate.state (delayed, conditional)
missing module named datasets - imported by accelerate.state (delayed, conditional), transformers.modeling_tf_utils (delayed), transformers.modelcard (delayed, conditional), transformers.models.rag.retrieval_rag (conditional), transformers.trainer (conditional), transformers.trainer_seq2seq (conditional)
missing module named 'torch_xla.distributed' - imported by torch.distributed.tensor._api (delayed, conditional, optional), accelerate.data_loader (conditional), accelerate.accelerator (conditional), accelerate.launchers (delayed, conditional), transformers.training_args (conditional), transformers.integrations.tpu (delayed, conditional), transformers.trainer (delayed, conditional, optional)
missing module named torchdata - imported by accelerate.data_loader (delayed, conditional)
missing module named 'deepspeed.checkpoint' - imported by accelerate.accelerator (delayed, conditional)
missing module named msamp - imported by accelerate.accelerator (delayed, conditional)
missing module named 'dvclive.plots' - imported by accelerate.tracking (delayed), transformers.integrations.integration_utils (delayed, conditional)
missing module named dvclive - imported by accelerate.tracking (delayed), transformers.integrations.integration_utils (delayed)
missing module named clearml - imported by accelerate.tracking (delayed), transformers.integrations.integration_utils (delayed, conditional)
missing module named mlflow - imported by accelerate.tracking (delayed), transformers.integrations.integration_utils (delayed)
missing module named aim - imported by accelerate.tracking (delayed)
missing module named comet_ml - imported by accelerate.tracking (delayed), transformers.integrations.integration_utils (delayed, conditional, optional)
missing module named wandb - imported by accelerate.tracking (delayed), transformers.integrations.integration_utils (delayed, conditional), transformers.trainer (delayed, conditional)
missing module named tensorboardX - imported by huggingface_hub._tensorboard_logger (conditional, optional), accelerate.tracking (delayed, optional), transformers.integrations.integration_utils (delayed, conditional, optional)
missing module named spqr_quant - imported by transformers.integrations.spqr (delayed, conditional)
missing module named 'peft.tuners' - imported by transformers.models.data2vec.modeling_data2vec_audio (delayed, conditional), transformers.models.unispeech_sat.modeling_unispeech_sat (delayed, conditional), transformers.models.wav2vec2.modeling_wav2vec2 (delayed, conditional), transformers.models.wav2vec2_bert.modeling_wav2vec2_bert (delayed, conditional), transformers.models.wav2vec2_conformer.modeling_wav2vec2_conformer (delayed, conditional), transformers.models.wavlm.modeling_wavlm (delayed, conditional), transformers.integrations.peft (delayed)
missing module named 'peft.utils' - imported by transformers.trainer (delayed, conditional), transformers.integrations.peft (delayed)
missing module named swanlab - imported by transformers.integrations.integration_utils (delayed)
missing module named 'dvclive.utils' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named flytekitplugins - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named flytekit - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named codecarbon - imported by transformers.integrations.integration_utils (delayed)
missing module named 'neptune.utils' - imported by transformers.integrations.integration_utils (delayed)
missing module named 'neptune.exceptions' - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named 'neptune.new' - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named 'neptune.internal' - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named neptune - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named dagshub - imported by transformers.integrations.integration_utils (delayed)
missing module named azureml - imported by transformers.integrations.integration_utils (delayed)
missing module named 'torch_xla.experimental' - imported by transformers.trainer (delayed, conditional, optional)
missing module named schedulefree - imported by transformers.trainer (delayed, conditional)
missing module named 'torchao.prototype' - imported by transformers.trainer (delayed, conditional)
missing module named 'torchao.optim' - imported by transformers.trainer (delayed, conditional)
missing module named grokadamw - imported by transformers.trainer (delayed, conditional)
missing module named apollo_torch - imported by transformers.trainer (delayed, conditional)
missing module named galore_torch - imported by transformers.trainer (delayed, conditional)
missing module named 'torchdistx.optimizers' - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'apex.optimizers' - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'torch_npu.optim' - imported by transformers.trainer (delayed, conditional, optional)
missing module named liger_kernel - imported by transformers.trainer (delayed, conditional)
missing module named 'smdistributed.modelparallel' - imported by transformers.trainer_pt_utils (conditional), transformers.training_args (conditional), transformers.modeling_utils (conditional), transformers.trainer (conditional)
missing module named 'torch_xla.debug' - imported by transformers.trainer (conditional)
missing module named apex - imported by transformers.models.longt5.modeling_longt5 (optional), transformers.trainer (conditional)
missing module named 'quark.torch' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_quark (delayed)
missing module named quark - imported by transformers.utils.quantization_config (delayed, conditional)
missing module named 'torchao.core' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_torchao (delayed, conditional)
missing module named 'torchao.dtypes' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_torchao (delayed)
missing module named 'torchao.quantization' - imported by transformers.modeling_utils (conditional), transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_torchao (delayed, conditional)
missing module named 'compressed_tensors.config' - imported by transformers.utils.quantization_config (delayed)
missing module named 'compressed_tensors.quantization' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_compressed_tensors (delayed, conditional)
missing module named compressed_tensors - imported by transformers.utils.quantization_config (delayed, conditional)
missing module named 'hqq.core' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_hqq (delayed, conditional), transformers.integrations.hqq (delayed, conditional)
missing module named 'sklearn.metrics' - imported by transformers.data.metrics (conditional)
missing module named transformers.AutoConfig - imported by transformers (delayed), transformers.utils.backbone_utils (delayed)
missing module named transformers.AutoBackbone - imported by transformers (delayed), transformers.utils.backbone_utils (delayed)
missing module named torch.ao.quantization.QuantStub - imported by torch.ao.quantization (top-level), torchvision.models.quantization.mobilenetv2 (top-level), torchvision.models.quantization.mobilenetv3 (top-level), torch.testing._internal.common_quantization (top-level)
missing module named torch.ao.quantization.DeQuantStub - imported by torch.ao.quantization (top-level), torchvision.models.quantization.mobilenetv2 (top-level), torchvision.models.quantization.mobilenetv3 (top-level), torch.testing._internal.common_quantization (top-level)
missing module named av - imported by transformers.video_utils (delayed), transformers.pipelines.video_classification (conditional), torchvision.io.video (optional), torchvision.io.video_reader (optional)
missing module named pycocotools - imported by transformers.models.conditional_detr.image_processing_conditional_detr (delayed, optional), transformers.models.conditional_detr.image_processing_conditional_detr_fast (delayed, optional), transformers.models.deformable_detr.image_processing_deformable_detr (delayed, optional), transformers.models.deformable_detr.image_processing_deformable_detr_fast (delayed, optional), transformers.models.deprecated.deta.image_processing_deta (delayed, optional), transformers.models.detr.image_processing_detr (delayed, optional), transformers.models.detr.image_processing_detr_fast (delayed, optional), transformers.models.grounding_dino.image_processing_grounding_dino (delayed, optional), transformers.models.grounding_dino.image_processing_grounding_dino_fast (delayed, optional), transformers.models.yolos.image_processing_yolos (delayed, optional), transformers.models.yolos.image_processing_yolos_fast (delayed, optional), torchvision.tv_tensors._dataset_wrapper (delayed)
missing module named scipy.sparse.coo_array - imported by scipy.sparse (delayed, conditional), scipy.io._fast_matrix_market (delayed, conditional)
missing module named lmdb - imported by torchvision.datasets.lsun (delayed)
missing module named accimage - imported by torchvision.transforms.transforms (optional), torchvision.transforms.functional (optional), torchvision.transforms._functional_pil (optional), torchvision.datasets.folder (delayed)
missing module named 'pycocotools.coco' - imported by torchvision.datasets.coco (delayed)
missing module named causal_conv1d - imported by transformers.models.jamba.modeling_jamba (conditional), transformers.models.bamba.modeling_bamba (conditional), transformers.models.falcon_mamba.modeling_falcon_mamba (conditional), transformers.models.granitemoehybrid.modeling_granitemoehybrid (conditional), transformers.models.mamba.modeling_mamba (conditional), transformers.models.mamba2.modeling_mamba2 (conditional), transformers.models.zamba.modeling_zamba (conditional), transformers.models.zamba2.modeling_zamba2 (conditional)
missing module named 'mamba_ssm.ops' - imported by transformers.models.jamba.modeling_jamba (conditional), transformers.models.bamba.modeling_bamba (conditional), transformers.models.falcon_mamba.modeling_falcon_mamba (conditional), transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (conditional), transformers.models.granitemoehybrid.modeling_granitemoehybrid (conditional), transformers.models.mamba.modeling_mamba (conditional), transformers.models.mamba2.modeling_mamba2 (conditional), transformers.models.zamba.modeling_zamba (conditional), transformers.models.zamba2.modeling_zamba2 (conditional)
missing module named einops - imported by transformers.integrations.npu_flash_attention (conditional), transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (top-level), torch._dynamo.decorators (delayed)
missing module named 'flash_attn.layers' - imported by transformers.modeling_flash_attention_utils (conditional), transformers.models.modernbert.modeling_modernbert (conditional)
missing module named flash_attn - imported by transformers.modeling_flash_attention_utils (conditional)
missing module named fast_lsh_cumulation - imported by transformers.models.yoso.modeling_yoso (delayed)
missing module named transformers.AutoTokenizer - imported by transformers (conditional), transformers.onnx.utils (delayed, conditional), transformers.models.deprecated.realm.retrieval_realm (top-level), transformers.models.mgp_str.processing_mgp_str (top-level)
missing module named transformers.AutoProcessor - imported by transformers (conditional), transformers.onnx.utils (delayed, conditional)
missing module named transformers.AutoFeatureExtractor - imported by transformers (conditional), transformers.onnx.utils (delayed, conditional)
missing module named tf2onnx - imported by transformers.onnx.convert (delayed)
missing module named onnx - imported by torch.onnx._internal.onnxruntime (conditional), torch.utils.tensorboard._onnx_graph (delayed), transformers.onnx.convert (delayed), torch.onnx._internal.onnx_proto_utils (delayed, optional), torch.onnx._internal.fx.serialization (delayed, conditional), torch.onnx._internal._exporter_legacy (delayed, conditional, optional), torch.onnx._internal.fx.type_utils (top-level), torch.onnx._internal.fx.onnxfunction_dispatcher (delayed), torch.onnx._internal._lazy_import (conditional), torch.onnx._internal.exporter._schemas (top-level), torch.onnx._internal.exporter._building (conditional), torch.onnx.verification (delayed, optional)
missing module named py3nvml - imported by onnxruntime.transformers.machine_info (top-level)
missing module named 'onnxruntime.training' - imported by onnxruntime.capi.onnxruntime_validation (delayed, optional)
missing module named sentencepiece - imported by transformers.convert_slow_tokenizer (delayed, conditional), transformers.models.albert.tokenization_albert (top-level), transformers.models.barthez.tokenization_barthez (top-level), transformers.models.bartpho.tokenization_bartpho (top-level), transformers.models.bert_generation.tokenization_bert_generation (top-level), transformers.models.bert_japanese.tokenization_bert_japanese (conditional), transformers.models.big_bird.tokenization_big_bird (top-level), transformers.models.camembert.tokenization_camembert (top-level), transformers.models.code_llama.tokenization_code_llama (top-level), transformers.models.cpm.tokenization_cpm (top-level), transformers.models.deberta_v2.tokenization_deberta_v2 (top-level), transformers.models.deprecated.ernie_m.tokenization_ernie_m (top-level), transformers.models.deprecated.xlm_prophetnet.tokenization_xlm_prophetnet (delayed, optional), transformers.models.fnet.tokenization_fnet (top-level), transformers.models.gemma.tokenization_gemma (top-level), transformers.models.siglip.tokenization_siglip (top-level), transformers.models.gpt_sw3.tokenization_gpt_sw3 (top-level), transformers.models.layoutxlm.tokenization_layoutxlm (top-level), transformers.models.xlm_roberta.tokenization_xlm_roberta (top-level), transformers.models.llama.tokenization_llama (top-level), transformers.models.m2m_100.tokenization_m2m_100 (top-level), transformers.models.marian.tokenization_marian (top-level), transformers.models.mbart.tokenization_mbart (top-level), transformers.models.mbart50.tokenization_mbart50 (top-level), transformers.models.mluke.tokenization_mluke (top-level), transformers.models.t5.tokenization_t5 (top-level), transformers.models.nllb.tokenization_nllb (top-level), transformers.models.pegasus.tokenization_pegasus (top-level), transformers.models.plbart.tokenization_plbart (top-level), transformers.models.reformer.tokenization_reformer (top-level), transformers.models.rembert.tokenization_rembert (top-level), transformers.models.seamless_m4t.tokenization_seamless_m4t (top-level), transformers.models.speech_to_text.tokenization_speech_to_text (top-level), transformers.models.speecht5.tokenization_speecht5 (top-level), transformers.models.udop.tokenization_udop (top-level), transformers.models.xglm.tokenization_xglm (top-level), transformers.models.xlnet.tokenization_xlnet (top-level)
missing module named 'mlx.core' - imported by transformers.utils.generic (delayed)
missing module named 'jax.core' - imported by transformers.utils.generic (delayed, conditional)
missing module named 'jax.random' - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named 'flax.serialization' - imported by transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level)
missing module named msgpack - imported by transformers.modeling_flax_utils (top-level)
missing module named flax - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_outputs (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.generation.flax_utils (top-level)
missing module named 'flax.traverse_util' - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named 'flax.linen' - imported by transformers.modeling_flax_utils (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named jieba - imported by transformers.models.cpm.tokenization_cpm (delayed, optional), transformers.models.cpm.tokenization_cpm_fast (delayed, optional), transformers.models.cpmant.tokenization_cpmant (conditional), transformers.models.xlm.tokenization_xlm (delayed, conditional, optional)
missing module named pythainlp - imported by transformers.models.xlm.tokenization_xlm (delayed, conditional, optional)
missing module named Mykytea - imported by transformers.models.flaubert.tokenization_flaubert (delayed, conditional, optional), transformers.models.herbert.tokenization_herbert (delayed, conditional, optional), transformers.models.xlm.tokenization_xlm (delayed, conditional, optional)
missing module named sacremoses - imported by transformers.models.biogpt.tokenization_biogpt (delayed, optional), transformers.models.deprecated.transfo_xl.tokenization_transfo_xl (conditional), transformers.models.flaubert.tokenization_flaubert (delayed, optional), transformers.models.fsmt.tokenization_fsmt (delayed, optional), transformers.models.herbert.tokenization_herbert (delayed, optional), transformers.models.marian.tokenization_marian (delayed, optional), transformers.models.xlm.tokenization_xlm (delayed, optional)
missing module named 'jax.lax' - imported by transformers.generation.flax_logits_process (top-level)
missing module named librosa - imported by transformers.audio_utils (conditional)
missing module named 'pyctcdecode.constants' - imported by transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (delayed)
missing module named 'pyctcdecode.alphabet' - imported by transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (delayed)
missing module named 'phonemizer.separator' - imported by transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (delayed)
missing module named 'phonemizer.backend' - imported by transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (delayed)
missing module named uroman - imported by transformers.models.vits.tokenization_vits (conditional)
missing module named phonemizer - imported by transformers.models.vits.tokenization_vits (conditional)
missing module named torch.nn.LogSoftmax - imported by torch.nn (top-level), transformers.models.visual_bert.modeling_visual_bert (top-level)
missing module named torch.nn.KLDivLoss - imported by torch.nn (top-level), transformers.models.visual_bert.modeling_visual_bert (top-level)
missing module named transformers.TFVisionEncoderDecoderModel - imported by transformers (delayed, conditional), transformers.models.vision_encoder_decoder.modeling_vision_encoder_decoder (delayed, conditional)
missing module named yt_dlp - imported by transformers.video_utils (delayed, conditional)
missing module named decord - imported by transformers.video_utils (delayed)
missing module named transformers.UdopConfig - imported by transformers (top-level), transformers.models.udop.modeling_udop (top-level)
missing module named transformers.models.timm_wrapper.TimmWrapperImageProcessor - imported by transformers.models.timm_wrapper (conditional, optional), transformers (conditional, optional)
missing module named transformers.models.timm_wrapper.processing_timm_wrapper - imported by transformers.models.timm_wrapper (conditional)
missing module named timm - imported by transformers.models.timm_backbone.modeling_timm_backbone (conditional), transformers.models.conditional_detr.modeling_conditional_detr (conditional), transformers.models.deformable_detr.modeling_deformable_detr (conditional), transformers.models.detr.modeling_detr (conditional), transformers.models.grounding_dino.modeling_grounding_dino (conditional), transformers.models.table_transformer.modeling_table_transformer (conditional), transformers.models.timm_wrapper.modeling_timm_wrapper (conditional)
missing module named 'timm.data' - imported by transformers.models.timm_wrapper.configuration_timm_wrapper (conditional)
missing module named tensorflow_probability - imported by transformers.models.groupvit.modeling_tf_groupvit (conditional, optional), transformers.models.tapas.modeling_tf_tapas (conditional, optional)
missing module named 'apex.normalization' - imported by transformers.models.t5.modeling_t5 (optional), transformers.models.pix2struct.modeling_pix2struct (optional), transformers.models.pop2piano.modeling_pop2piano (optional)
missing module named torch.nn.L1Loss - imported by torch.nn (top-level), transformers.models.speecht5.modeling_speecht5 (top-level)
missing module named 'torchaudio.compliance' - imported by transformers.models.speech_to_text.feature_extraction_speech_to_text (conditional)
missing module named num2words - imported by transformers.models.smolvlm.processing_smolvlm (conditional)
missing module named torch.nn.LayerNorm - imported by torch.nn (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.deprecated.jukebox.modeling_jukebox (top-level), transformers.models.deprecated.xlm_prophetnet.modeling_xlm_prophetnet (top-level), transformers.models.esm.modeling_esmfold (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.fsmt.modeling_fsmt (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.prophetnet.modeling_prophetnet (top-level), transformers.models.qwen2_vl.modeling_qwen2_vl (top-level), transformers.models.sew_d.modeling_sew_d (top-level)
missing module named 'tensorflow.experimental' - imported by transformers.models.sam.image_processing_sam (conditional), keras.src.backend.tensorflow.distribution_lib (top-level)
missing module named rjieba - imported by transformers.models.roformer.tokenization_roformer (delayed, optional), transformers.models.roformer.tokenization_utils (delayed, optional)
missing module named transformers.RegNetConfig - imported by transformers (top-level), transformers.models.regnet.modeling_flax_regnet (top-level)
missing module named faiss - imported by transformers.models.rag.retrieval_rag (conditional)
missing module named spacy - imported by transformers.models.openai.tokenization_openai (delayed, optional)
missing module named ftfy - imported by transformers.models.clip.tokenization_clip (delayed, optional), transformers.models.openai.tokenization_openai (delayed, optional)
missing module named Levenshtein - imported by transformers.models.nougat.tokenization_nougat_fast (conditional)
missing module named 'flash_attn.ops' - imported by transformers.models.modernbert.modeling_modernbert (conditional)
missing module named 'flash_attn.flash_attn_interface' - imported by transformers.models.modernbert.modeling_modernbert (conditional)
missing module named transformers.models.mistral3.processing_mistral3 - imported by transformers.models.mistral3 (conditional)
missing module named transformers.MaskFormerForInstanceSegmentationOutput - imported by transformers (conditional), transformers.models.maskformer.image_processing_maskformer (conditional)
missing module named 'mambapy.pscan' - imported by transformers.models.mamba.modeling_mamba (conditional)
missing module named torch.nn.SmoothL1Loss - imported by torch.nn (top-level), transformers.models.lxmert.modeling_lxmert (top-level)
missing module named 'detectron2.modeling' - imported by transformers.models.layoutlmv2.modeling_layoutlmv2 (conditional)
missing module named detectron2 - imported by transformers.models.layoutlmv2.configuration_layoutlmv2 (conditional), transformers.models.layoutlmv2.modeling_layoutlmv2 (conditional)
missing module named sklearn - imported by transformers.generation.candidate_generator (conditional), nltk.parse.transitionparser (optional)
missing module named torchaudio - imported by transformers.models.audio_spectrogram_transformer.feature_extraction_audio_spectrogram_transformer (conditional), transformers.models.granite_speech.feature_extraction_granite_speech (conditional), transformers.pipelines.audio_classification (delayed, conditional), transformers.pipelines.automatic_speech_recognition (delayed, conditional), torch.utils.data.datapipes.utils.decoder (delayed, optional)
missing module named keras_nlp - imported by transformers.models.gpt2.tokenization_gpt2_tf (conditional)
missing module named tensorflow_text - imported by transformers.models.bert.tokenization_bert_tf (top-level), transformers.models.bert_generation.modeling_bert_generation (delayed, optional), transformers.models.gpt2.tokenization_gpt2_tf (top-level)
missing module named transformers.models.funnel.convert_funnel_original_tf_checkpoint_to_pytorch - imported by transformers.models.funnel (conditional)
missing module named g2p_en - imported by transformers.models.fastspeech2_conformer.tokenization_fastspeech2_conformer (delayed, optional)
missing module named selective_scan_cuda - imported by transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (top-level)
missing module named causal_conv1d_cuda - imported by transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (optional)
missing module named mambapy - imported by transformers.models.falcon_mamba.modeling_falcon_mamba (conditional)
missing module named transformers.TFEncoderDecoderModel - imported by transformers (delayed, conditional), transformers.models.encoder_decoder.modeling_encoder_decoder (delayed, conditional)
missing module named 'natten.functional' - imported by transformers.models.dinat.modeling_dinat (conditional)
missing module named scann - imported by transformers.models.deprecated.realm.retrieval_realm (delayed)
missing module named 'pytorch_quantization.nn' - imported by transformers.models.deprecated.qdqbert.modeling_qdqbert (conditional, optional)
missing module named pytorch_quantization - imported by transformers.models.deprecated.qdqbert.modeling_qdqbert (conditional, optional)
missing module named xformers - imported by transformers.models.deprecated.open_llama.modeling_open_llama (optional)
missing module named natten - imported by transformers.models.deprecated.nat.modeling_nat (conditional)
missing module named soundfile - imported by transformers.models.csm.processing_csm (conditional)
missing module named transformers.AutoModelForImageTextToText - imported by transformers (top-level), transformers.models.colpali.modeling_colpali (top-level)
missing module named emoji - imported by transformers.models.bertweet.tokenization_bertweet (delayed, optional)
missing module named rhoknp - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, optional)
missing module named sudachipy - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, optional)
missing module named unidic - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, conditional, optional)
missing module named unidic_lite - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, conditional, optional)
missing module named ipadic - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, conditional, optional)
missing module named fugashi - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, optional)
missing module named tensorflow_hub - imported by transformers.models.bert_generation.modeling_bert_generation (delayed, optional)
missing module named transformers.models.bamba.processing_bamba - imported by transformers.models.bamba (conditional)
missing module named 'wandb.sdk' - imported by transformers.integrations.integration_utils (delayed)
missing module named 'wandb.env' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named sigopt - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named 'datasets.load' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named 'ray.tune' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named 'ray.train' - imported by transformers.integrations.integration_utils (delayed), transformers.trainer (delayed, conditional)
missing module named ray - imported by transformers.trainer_utils (delayed), transformers.integrations.integration_utils (delayed)
missing module named optuna - imported by transformers.integrations.integration_utils (delayed, conditional), transformers.trainer (delayed, conditional)
missing module named kernels - imported by transformers.integrations.hub_kernels (optional), transformers.modeling_utils (conditional)
missing module named fast_hadamard_transform - imported by transformers.integrations.higgs (conditional)
missing module named 'flute.tune' - imported by transformers.integrations.higgs (conditional)
missing module named 'flute.integrations' - imported by transformers.integrations.higgs (conditional)
missing module named 'triton.language' - imported by torch._inductor.codegen.triton_split_scan (delayed), torch._inductor.codegen.wrapper (delayed), transformers.integrations.finegrained_fp8 (conditional), torch.sparse._triton_ops (conditional), torch._inductor.runtime.triton_helpers (top-level), torch.testing._internal.triton_utils (conditional)
missing module named triton - imported by torch._utils_internal (delayed, conditional), torch._dynamo.logging (conditional, optional), torch.utils._triton (delayed), torch._higher_order_ops.triton_kernel_wrap (delayed), torch._inductor.runtime.autotune_cache (conditional), torch._inductor.runtime.coordinate_descent_tuner (optional), torch._inductor.runtime.triton_heuristics (conditional, optional), torch._inductor.kernel.mm_common (delayed), torch._inductor.kernel.mm_plus_mm (delayed), torch._inductor.codegen.wrapper (delayed, conditional), transformers.integrations.finegrained_fp8 (conditional), torch.sparse._triton_ops_meta (delayed, conditional), torch.sparse._triton_ops (conditional), torch._dynamo.utils (conditional), torch._inductor.compile_worker.__main__ (optional), torch._inductor.runtime.triton_helpers (top-level), torch.testing._internal.triton_utils (conditional)
missing module named fbgemm_gpu - imported by transformers.integrations.fbgemm_fp8 (conditional)
missing module named eetq - imported by transformers.quantizers.quantizer_eetq (delayed, optional), transformers.integrations.eetq (conditional)
missing module named 'awq.quantize' - imported by transformers.integrations.awq (delayed, conditional)
missing module named 'awq.modules' - imported by transformers.integrations.awq (delayed, conditional)
missing module named awq - imported by transformers.integrations.awq (delayed)
missing module named aqlm - imported by transformers.integrations.aqlm (delayed)
missing module named tiktoken - imported by transformers.convert_slow_tokenizer (delayed, optional)
missing module named sentencepiece_model_pb2 - imported by tokenizers.implementations.sentencepiece_unigram (delayed, optional)
missing module named 'tf_keras.optimizers' - imported by transformers.optimization_tf (optional), tensorflow.python.saved_model.load (delayed, conditional, optional)
missing module named 'transformers.utils.dummies_sentencepiece_and_tokenizers_objects' - imported by transformers (conditional, optional)
missing module named 'optimum.bettertransformer' - imported by transformers.modeling_utils (delayed)
missing module named 'optimum.version' - imported by transformers.modeling_utils (delayed)
missing module named torch.nn.ModuleList - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level), transformers.quantizers.base (conditional)
missing module named 'flute.utils' - imported by transformers.quantizers.quantizer_higgs (delayed)
missing module named flute - imported by transformers.quantizers.quantizer_higgs (delayed)
missing module named 'optimum.gptq' - imported by transformers.quantizers.quantizer_gptq (delayed)
missing module named 'compressed_tensors.compressors' - imported by transformers.quantizers.quantizer_compressed_tensors (delayed)
missing module named 'auto_round.inference' - imported by transformers.quantizers.quantizer_auto_round (delayed, conditional)
missing module named auto_round - imported by transformers.quantizers.quantizer_auto_round (delayed)
missing module named torch.nn.Identity - imported by torch.nn (top-level), transformers.modeling_utils (top-level)
missing module named torch.nn.BCELoss - imported by torch.nn (top-level), transformers.generation.watermarking (top-level)
missing module named diffusers - imported by gradio.pipelines_utils (delayed, optional), gradio.pipelines (conditional), gradio.interface (conditional)
missing module named transformers_js_py - imported by gradio.pipelines_utils (delayed, optional)
missing module named sounddevice - imported by openai._extras.sounddevice_proxy (delayed, conditional, optional)
missing module named jiter.from_json - imported by jiter (top-level), openai.lib.streaming.chat._completions (top-level)
missing module named pyaudioop - imported by pydub.utils (optional)
missing module named 'mcp.server' - imported by gradio.mcp (top-level)
missing module named spaces - imported by gradio.blocks (optional)
missing module named altair - imported by gradio.components.plot (delayed)
missing module named 'bokeh.embed' - imported by gradio.components.plot (delayed, conditional)
missing module named bokeh - imported by gradio.components.plot (delayed, optional)
missing module named polars - imported by gradio.components.dataframe (delayed, conditional)
missing module named a2wsgi - imported by uvicorn.middleware.wsgi (optional)
missing module named 'gunicorn.workers' - imported by uvicorn.workers (top-level)
missing module named gunicorn - imported by uvicorn.workers (top-level)
missing module named watchfiles - imported by uvicorn.supervisors.watchfilesreload (top-level)
missing module named httptools - imported by uvicorn.protocols.http.httptools_impl (top-level), uvicorn.protocols.http.auto (optional)
missing module named 'pyodide.http' - imported by gradio.analytics (optional)
missing module named 'authlib.integrations' - imported by huggingface_hub._oauth (delayed, optional), gradio.oauth (delayed, optional)
missing module named pyodide - imported by gradio.processing_utils (conditional)
missing module named authlib - imported by huggingface_hub._oauth (delayed, optional)
missing module named 'ipywidgets.widgets' - imported by huggingface_hub._login (delayed, optional)
missing module named 'InquirerPy.separator' - imported by huggingface_hub.commands.delete_cache (optional)
missing module named 'InquirerPy.base' - imported by huggingface_hub.commands.delete_cache (optional)
missing module named InquirerPy - imported by huggingface_hub.commands.delete_cache (optional)
missing module named 'IPython.utils' - imported by h5py.ipy_completer (top-level)
missing module named mpi4py - imported by h5py._hl.files (delayed)
missing module named keras.src.backend.random_seed_dtype - imported by keras.src.backend (delayed), keras.src.random.seed_generator (delayed)
missing module named keras.src.backend.convert_to_tensor - imported by keras.src.backend (delayed), keras.src.random.seed_generator (delayed)
missing module named keras.src.backend.is_tensor - imported by keras.src.backend (top-level), keras.src.ops (top-level)
missing module named keras.src.backend.cond - imported by keras.src.backend (top-level), keras.src.ops (top-level)
missing module named keras.src.backend.cast - imported by keras.src.backend (top-level), keras.src.ops (top-level)
missing module named 'tensorflow.summary' - imported by keras.src.callbacks.tensorboard (delayed, conditional)
missing module named oauth2client - imported by googleapiclient._auth (optional), tensorflow.python.tpu.client.client (optional)
missing module named 'oauth2client.locked_file' - imported by googleapiclient.discovery_cache.file_cache (optional)
missing module named 'oauth2client.contrib' - imported by googleapiclient.discovery_cache.file_cache (optional)
missing module named 'oauth2client.client' - imported by googleapiclient._auth (optional), tensorflow.python.distribute.cluster_resolver.gce_cluster_resolver (optional)
missing module named ca_certs_locater - imported by httplib2.certs (optional)
missing module named railroad - imported by pyparsing.diagram (top-level)
missing module named pyparsing.Word - imported by pyparsing (delayed), pyparsing.unicode (delayed)
missing module named 'tensorflow.contrib' - imported by tensorflow.python.tools.import_pb_to_tensorboard (optional)
missing module named 'keras.optimizers' - imported by tensorflow.python.saved_model.load (delayed, conditional, optional)
missing module named rules_python - imported by tensorflow.python.platform.resource_loader (optional)
missing module named tensorflow.python.keras.layers.wrappers - imported by tensorflow.python.keras.layers (delayed), tensorflow.python.keras.utils.vis_utils (delayed)
missing module named 'six.moves.urllib.request' - imported by tensorflow.python.keras.utils.data_utils (top-level)
missing module named tensorflow.python.keras.__version__ - imported by tensorflow.python.keras (delayed), tensorflow.python.keras.saving.saving_utils (delayed), tensorflow.python.keras.saving.hdf5_format (delayed), tensorflow.python.keras.engine.training (delayed)
missing module named portpicker - imported by tensorflow.python.framework.test_util (delayed), tensorflow.dtensor.python.tests.multi_client_test_util (top-level), tensorflow.python.debug.lib.grpc_debug_test_server (top-level)
missing module named 'tensorflow.python.framework.is_mlir_bridge_test_true' - imported by tensorflow.python.framework.test_util (optional)
missing module named 'tensorflow.python.framework.is_mlir_bridge_test_false' - imported by tensorflow.python.framework.test_util (optional)
missing module named 'tensorflow.python.framework.is_xla_test_true' - imported by tensorflow.python.framework.test_util (optional)
missing module named 'tensorflow.python.platform.cpp_memory_checker' - imported by tensorflow.python.framework.memory_checker (optional)
missing module named memory_profiler - imported by tensorflow.python.eager.memory_tests.memory_test_util (optional)
missing module named objgraph - imported by tensorflow.python.distribute.test_util (optional)
missing module named tblib - imported by tensorflow.python.distribute.multi_process_runner (optional)
missing module named dill - imported by torch.utils._import_utils (delayed), torch.utils.data.graph (delayed, conditional, optional), tensorflow.python.distribute.multi_process_runner (optional)
missing module named six.moves.urllib.request - imported by six.moves.urllib (top-level), tensorflow.python.distribute.failure_handling.failure_handling_util (top-level)
missing module named cloud_tpu_client - imported by tensorflow.python.distribute.cluster_resolver.tpu.tpu_cluster_resolver (optional)
missing module named kubernetes - imported by tensorflow.python.distribute.cluster_resolver.kubernetes_cluster_resolver (delayed, conditional, optional)
missing module named grpc_reflection - imported by grpc (optional)
missing module named grpc_health - imported by grpc (optional)
missing module named grpc_tools - imported by grpc._runtime_protos (delayed, optional), grpc (optional)
missing module named 'grpc_tools.protoc' - imported by grpc._runtime_protos (delayed, conditional)
missing module named astn - imported by gast.ast2 (top-level)
missing module named tflite_runtime - imported by tensorflow.lite.python.metrics.metrics (conditional), tensorflow.lite.python.interpreter (conditional), tensorflow.lite.python.analyzer (conditional), tensorflow.lite.tools.visualize (conditional)
missing module named tensorflow_io_gcs_filesystem - imported by tensorflow (conditional), tensorflow._api.v2.v2 (conditional)
missing module named theano - imported by opt_einsum.backends.theano (delayed)
missing module named tensorflow.python.framework.fast_tensor_util - imported by tensorflow.python.framework (optional), tensorflow.python.framework.tensor_util (optional)
missing module named tensorboard.compat.notf - imported by tensorboard.compat (delayed, optional)
missing module named 'matplotlib.figure' - imported by pandas.plotting._misc (conditional), torch.utils.tensorboard.writer (conditional), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.misc (conditional), pandas.plotting._matplotlib.core (conditional), pandas.plotting._matplotlib.boxplot (conditional), pandas.plotting._matplotlib.hist (conditional)
missing module named moviepy - imported by torch.utils.tensorboard.summary (delayed, optional)
missing module named 'matplotlib.backends' - imported by torch.utils.tensorboard._utils (delayed)
missing module named tabulate - imported by torch.ao.ns.fx.n_shadows_utils (delayed, optional), torch._inductor.wrapper_benchmark (delayed), torch.utils.flop_counter (delayed), torch.fx.graph (delayed, optional), torch._dynamo.utils (delayed, optional), torch._dynamo.backends.distributed (delayed, conditional, optional), torch.ao.quantization.fx._model_report.model_report_visualizer (optional), torch.distributed._tools.mem_tracker (delayed, optional), torch.distributed.tensor.debug._visualize_sharding (delayed, optional), torch.distributed.tensor.debug._op_coverage (delayed), torch.utils.benchmark.utils.compile (optional)
missing module named torcharrow - imported by torch.utils.data.datapipes.iter.callable (delayed, conditional, optional)
missing module named 'conda.cli' - imported by torch.utils.benchmark.examples.blas_compare_setup (optional)
missing module named conda - imported by torch.utils.benchmark.examples.blas_compare_setup (optional)
missing module named 'triton.runtime' - imported by torch.utils._triton (delayed), torch._higher_order_ops.triton_kernel_wrap (delayed), torch._library.triton (delayed), torch._inductor.runtime.triton_heuristics (conditional), torch._inductor.select_algorithm (delayed, optional), torch._inductor.ir (delayed), torch._dynamo.variables.builder (delayed, conditional), torch._inductor.fx_passes.reinplace (delayed, conditional), torch._inductor.utils (delayed)
missing module named 'triton.compiler' - imported by torch.utils._triton (delayed, optional), torch._higher_order_ops.triton_kernel_wrap (delayed), torch._inductor.runtime.hints (optional), torch._inductor.runtime.triton_heuristics (conditional, optional), torch._inductor.codegen.triton (delayed), torch._inductor.scheduler (delayed), torch._inductor.codecache (delayed, optional), torch._inductor.async_compile (delayed, optional)
missing module named 'torch._C._profiler' - imported by torch.utils._traceback (delayed), torch.profiler (top-level), torch.autograd.profiler (top-level), torch.profiler.profiler (top-level), torch.profiler._memory_profiler (top-level), torch.cuda._memory_viz (delayed), torch.testing._internal.logging_tensor (top-level), torch.autograd (top-level), torch.profiler._pattern_matcher (top-level)
missing module named flint - imported by sympy.external.gmpy (delayed, optional), sympy.polys.polyutils (conditional), sympy.polys.factortools (conditional), sympy.polys.polyclasses (conditional), sympy.polys.domains.groundtypes (conditional), sympy.polys.domains.finitefield (conditional)
missing module named py - imported by mpmath.tests.runtests (delayed, conditional)
missing module named 'matplotlib.tri' - imported by mpl_toolkits.mplot3d.axes3d (top-level)
missing module named 'matplotlib.container' - imported by mpl_toolkits.mplot3d.axes3d (top-level)
missing module named 'matplotlib.image' - imported by mpl_toolkits.mplot3d.axes3d (top-level)
missing module named 'matplotlib.pylab' - imported by pylab (top-level)
missing module named 'sage.libs' - imported by mpmath.libmp.backend (conditional, optional), mpmath.libmp.libelefun (conditional, optional), mpmath.libmp.libmpf (conditional, optional), mpmath.libmp.libmpc (conditional, optional), mpmath.libmp.libhyper (delayed, conditional), mpmath.ctx_mp (conditional)
missing module named sage - imported by mpmath.libmp.backend (conditional, optional)
missing module named gmpy - imported by mpmath.libmp.backend (conditional, optional)
missing module named gmpy2 - imported by mpmath.libmp.backend (conditional, optional), sympy.polys.domains.groundtypes (conditional), sympy.testing.runtests (delayed, conditional)
missing module named 'pyglet.image' - imported by sympy.printing.preview (delayed, optional)
missing module named 'pyglet.window' - imported by sympy.plotting.pygletplot.managed_window (top-level), sympy.plotting.pygletplot.plot_controller (top-level), sympy.printing.preview (delayed, optional)
missing module named pyglet - imported by sympy.plotting.pygletplot.plot (optional), sympy.plotting.pygletplot.plot_axes (top-level), sympy.printing.preview (delayed, conditional, optional), sympy.testing.runtests (delayed, conditional)
missing module named 'pyglet.gl' - imported by sympy.plotting.pygletplot.plot_axes (top-level), sympy.plotting.pygletplot.util (top-level), sympy.plotting.pygletplot.plot_window (top-level), sympy.plotting.pygletplot.plot_camera (top-level), sympy.plotting.pygletplot.plot_rotation (top-level), sympy.plotting.pygletplot.plot_curve (top-level), sympy.plotting.pygletplot.plot_mode_base (top-level), sympy.plotting.pygletplot.plot_surface (top-level)
missing module named 'pyglet.clock' - imported by sympy.plotting.pygletplot.managed_window (top-level)
missing module named all - imported by sympy.testing.runtests (delayed, optional)
missing module named 'IPython.Shell' - imported by sympy.interactive.session (delayed, conditional)
missing module named 'IPython.frontend' - imported by sympy.interactive.printing (delayed, conditional, optional), sympy.interactive.session (delayed, conditional)
missing module named 'IPython.terminal' - imported by sympy.interactive.printing (delayed, conditional, optional), sympy.interactive.session (delayed, conditional)
missing module named 'IPython.iplib' - imported by sympy.interactive.printing (delayed, optional)
missing module named pysat - imported by sympy.logic.algorithms.minisat22_wrapper (delayed)
missing module named pycosat - imported by sympy.logic.algorithms.pycosat_wrapper (delayed)
missing module named 'sage.all' - imported by sympy.core.function (delayed)
missing module named 'sage.interfaces' - imported by sympy.core.basic (delayed)
missing module named 'hypothesis.strategies' - imported by torch.testing._internal.hypothesis_utils (top-level)
missing module named 'hypothesis.extra' - imported by torch.testing._internal.hypothesis_utils (top-level)
missing module named 'torch._C._distributed_c10d' - imported by torch.distributed (conditional), torch.distributed.distributed_c10d (top-level), torch.distributed.constants (top-level), torch.distributed.rpc (conditional), torch.distributed.tensor._collective_utils (top-level), torch.distributed._shard.sharded_tensor.reshard (top-level), torch.distributed._shard.sharding_spec.chunk_sharding_spec_ops.embedding_bag (top-level), torch.testing._internal.distributed.fake_pg (top-level), torch._dynamo.variables.distributed (delayed), torch.distributed._symmetric_memory (top-level), torch.distributed.elastic.control_plane (delayed), torch.testing._internal.distributed.multi_threaded_pg (top-level)
missing module named 'xmlrunner.result' - imported by torch.testing._internal.common_utils (delayed, conditional)
missing module named torch.nn.Sequential - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ParameterList - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ParameterDict - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ModuleDict - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named expecttest - imported by torch.testing._internal.common_utils (top-level)
missing module named torch.ao.quantization.QConfigMapping - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.custom_config (top-level), torch.ao.ns.fx.n_shadows_utils (top-level), torch.ao.ns.fx.qconfig_multi_mapping (top-level), torch.ao.ns._numeric_suite_fx (top-level), torch.ao.quantization.fx.lstm_utils (top-level), torch.ao.quantization.pt2e.prepare (top-level), torch.testing._internal.common_quantization (top-level)
missing module named torch.ao.quantization.float_qparams_weight_only_qconfig - imported by torch.ao.quantization (delayed, conditional), torch.ao.nn.quantized.modules.embedding_ops (delayed, conditional), torch.testing._internal.common_quantization (top-level)
missing module named torch.ao.quantization.QConfig - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.qconfig_mapping_utils (top-level), torch.ao.quantization.fx.lstm_utils (top-level), torch.testing._internal.common_quantization (top-level)
missing module named torch.ao.quantization.QuantType - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.utils (top-level), torch.testing._internal.common_quantization (top-level)
missing module named 'torch._C._autograd' - imported by torch._subclasses.meta_utils (top-level), torch.profiler (top-level), torch.profiler._memory_profiler (top-level), torch.autograd (top-level)
missing module named torch.nn.Module - imported by torch.nn (top-level), torch.optim.swa_utils (top-level), torch.ao.quantization.fake_quantize (top-level), torch.jit._recursive (top-level), torch.jit._script (top-level), torch.jit._trace (top-level), torch._dynamo.mutation_guard (top-level), torch.distributed.nn.api.remote_module (top-level), torch.fx.passes.utils.common (top-level), torch.fx.experimental.proxy_tensor (top-level)
missing module named 'torch._C._onnx' - imported by torch.onnx (top-level), torch.onnx.symbolic_helper (top-level), torch.onnx.utils (top-level), torch.onnx._globals (top-level), torch.onnx.symbolic_opset9 (top-level), torch.onnx.symbolic_opset10 (top-level), torch.onnx.symbolic_opset13 (top-level), torch.onnx._experimental (top-level), torch.onnx.verification (top-level)
missing module named 'onnxscript.rewriter' - imported by torch.onnx._internal.onnxruntime (delayed, conditional, optional)
missing module named onnxscript - imported by torch.onnx._internal.fx.registration (conditional), torch.onnx._internal._exporter_legacy (delayed, conditional, optional), torch.onnx._internal.fx.diagnostics (top-level), torch.onnx._internal.fx.onnxfunction_dispatcher (conditional), torch.onnx._internal.fx.fx_onnx_interpreter (top-level), torch.onnx._internal.onnxruntime (delayed, conditional, optional), torch.onnx._internal._lazy_import (conditional), torch.onnx._internal.exporter._core (top-level), torch.onnx._internal.exporter._dispatching (top-level), torch.onnx._internal.exporter._schemas (top-level), torch.onnx._internal.exporter._registration (conditional), torch.onnx._internal.exporter._building (top-level), torch.onnx._internal.exporter._tensors (top-level), torch.onnx._internal.exporter._ir_passes (top-level), torch.onnx._internal.exporter._reporting (conditional)
missing module named 'onnx.defs' - imported by torch.onnx._internal.fx.type_utils (delayed, conditional)
missing module named 'onnxscript.function_libs' - imported by torch.onnx._internal.fx.diagnostics (top-level), torch.onnx._internal.fx.onnxfunction_dispatcher (conditional), torch.onnx._internal.fx.decomposition_skip (top-level), torch.onnx._internal.fx.fx_onnx_interpreter (top-level), torch.onnx._internal.exporter._ir_passes (delayed, optional)
missing module named 'onnxscript._framework_apis' - imported by torch.onnx._internal._exporter_legacy (delayed), torch.onnx._internal._lazy_import (conditional)
missing module named pyinstrument - imported by torch.onnx._internal.exporter._core (delayed, conditional)
missing module named 'onnxscript.ir' - imported by torch.onnx._internal.exporter._core (top-level), torch.onnx._internal.exporter._building (top-level)
missing module named 'onnxscript.evaluator' - imported by torch.onnx._internal.exporter._core (top-level)
missing module named torch.multiprocessing._prctl_pr_set_pdeathsig - imported by torch.multiprocessing (top-level), torch.multiprocessing.spawn (top-level)
missing module named 'torch._C._monitor' - imported by torch.monitor (top-level)
missing module named 'torch._C._jit_tree_views' - imported by torch._sources (top-level), torch.jit.frontend (top-level)
missing module named torch.TensorType - imported by torch (top-level), torch.jit._passes._property_propagation (top-level)
missing module named 'monkeytype.tracing' - imported by torch.jit._monkeytype_config (optional)
missing module named 'monkeytype.db' - imported by torch.jit._monkeytype_config (optional)
missing module named 'monkeytype.config' - imported by torch.jit._monkeytype_config (optional)
missing module named monkeytype - imported by torch.jit._monkeytype_config (optional)
missing module named z3 - imported by torch.fx.experimental.validator (optional), torch.fx.experimental.migrate_gradual_types.transform_to_z3 (optional), torch.fx.experimental.migrate_gradual_types.z3_types (optional)
missing module named 'torch._C._distributed_rpc' - imported by torch.distributed.rpc (conditional), torch.distributed.rpc.api (top-level), torch.distributed.rpc.constants (top-level), torch.distributed.rpc.internal (top-level), torch.distributed.rpc.options (top-level), torch._jit_internal (conditional)
missing module named 'torch._C._distributed_rpc_testing' - imported by torch.distributed.rpc._testing (conditional)
missing module named torchdistx - imported by torch.distributed.fsdp._init_utils (optional)
missing module named etcd - imported by torch.distributed.elastic.rendezvous.etcd_rendezvous (top-level), torch.distributed.elastic.rendezvous.etcd_store (top-level), torch.distributed.elastic.rendezvous.etcd_rendezvous_backend (top-level), torch.distributed.elastic.rendezvous.etcd_server (optional)
missing module named 'torch.distributed.elastic.metrics.static_init' - imported by torch.distributed.elastic.metrics (optional)
missing module named 'torch._C._distributed_autograd' - imported by torch.distributed.autograd (conditional)
missing module named amdsmi - imported by torch.cuda (conditional, optional), torch.cuda.memory (delayed, conditional, optional)
missing module named 'coremltools.models' - imported by torch.backends._coreml.preprocess (top-level)
missing module named 'coremltools.converters' - imported by torch.backends._coreml.preprocess (top-level)
missing module named coremltools - imported by torch.backends._coreml.preprocess (top-level)
missing module named torch.ao.quantization.QConfigAny - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.utils (top-level)
missing module named pytorch_lightning - imported by torch.ao.pruning._experimental.data_sparsifier.lightning.callbacks.data_sparsity (top-level)
missing module named torch.nn.ReLU - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Linear - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv3d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv2d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv1d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm3d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm2d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm1d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named 'torch._C._functorch' - imported by torch._subclasses.fake_tensor (top-level), torch._subclasses.meta_utils (top-level), torch._functorch.pyfunctorch (top-level), torch._higher_order_ops.cond (top-level), torch._functorch.autograd_function (top-level), torch._functorch.utils (top-level), torch._functorch.vmap (top-level), torch._functorch.eager_transforms (top-level)
missing module named torch._numpy.float_ - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.max - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.isnan - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.signbit - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.real - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.isscalar - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.iscomplexobj - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.imag - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.intp - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named torch._numpy.empty - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named torch._numpy.arange - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named fbscribelogger - imported by torch._logging.scribe (optional)
missing module named 'torch._C._lazy_ts_backend' - imported by torch._lazy.ts_backend (top-level), torch._lazy.computation (top-level)
missing module named 'torch._C._lazy' - imported by torch._lazy (top-level), torch._lazy.device_context (top-level), torch._lazy.metrics (top-level), torch._lazy.computation (top-level), torch._lazy.config (top-level), torch._lazy.debug (top-level), torch._lazy.ir_cache (top-level)
missing module named 'triton.testing' - imported by torch._inductor.runtime.benchmarking (delayed, optional), torch._inductor.utils (delayed)
missing module named 'ck4inductor.universal_gemm' - imported by torch._inductor.utils (delayed, optional)
missing module named ck4inductor - imported by torch._inductor.utils (delayed, optional)
missing module named 'triton.backends' - imported by torch._inductor.runtime.triton_heuristics (conditional, optional)
missing module named halide - imported by torch._inductor.codecache (delayed, conditional), torch._inductor.runtime.halide_helpers (optional)
missing module named 'torch._inductor.fb' - imported by torch._inductor.runtime.autotune_cache (delayed, conditional, optional), torch._inductor.cpp_builder (conditional), torch._inductor.graph (conditional), torch._inductor.codecache (delayed, conditional, optional), torch._inductor.compile_fx (delayed, conditional, optional)
missing module named rfe - imported by torch._inductor.remote_cache (conditional)
missing module named hiredis - imported by redis.utils (optional), redis.connection (conditional), redis._parsers.hiredis (delayed)
missing module named async_timeout - imported by redis._parsers.base (conditional), redis.asyncio.connection (conditional), redis._parsers.hiredis (conditional)
missing module named torch._inductor.fx_passes.fb - imported by torch._inductor.fx_passes (delayed, conditional), torch._inductor.fx_passes.pre_grad (delayed, conditional)
missing module named deeplearning - imported by torch._inductor.fx_passes.group_batch_fusion (optional)
missing module named 'triton.fb' - imported by torch._inductor.cpp_builder (conditional), torch._inductor.codecache (conditional)
missing module named 'libfb.py' - imported by torch._dynamo.debug_utils (conditional), torch._inductor.codecache (delayed, conditional), torch._inductor.compile_worker.subproc_pool (delayed, conditional)
missing module named 'cutlass_library.gemm_operation' - imported by torch._inductor.codegen.cuda.gemm_template (delayed), torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions (conditional)
missing module named 'cutlass_library.library' - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional), torch._inductor.codegen.cuda.gemm_template (delayed), torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions (conditional)
missing module named 'cutlass_library.generator' - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed)
missing module named 'cutlass_library.manifest' - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional)
missing module named cutlass_library - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional)
missing module named 'torch._C._dynamo' - imported by torch._guards (top-level), torch._dynamo.convert_frame (top-level), torch._dynamo.guards (top-level), torch._dynamo.eval_frame (top-level), torch._dynamo.decorators (conditional), torch._dynamo.types (top-level)
missing module named 'torch_xla.stablehlo' - imported by torch._functorch.fx_minifier (delayed)
missing module named 'torch.utils._config_typing' - imported by torch._dynamo.config (conditional), torch._inductor.config (conditional), torch._functorch.config (conditional)
missing module named foo - imported by torch._functorch.compilers (delayed)
missing module named torchrec - imported by torch._dynamo.variables.user_defined (delayed)
missing module named '_pytest.recwarn' - imported by torch._dynamo.variables.user_defined (delayed, optional)
missing module named '_pytest.python_api' - imported by torch._dynamo.variables.user_defined (delayed, optional)
missing module named 'einops._torch_specific' - imported by torch._dynamo.decorators (delayed, optional)
missing module named 'tvm.contrib' - imported by torch._dynamo.backends.tvm (delayed)
missing module named tvm - imported by torch._dynamo.backends.tvm (delayed, conditional)
missing module named libfb - imported by torch._inductor.config (conditional, optional)
missing module named 'torch._C._VariableFunctions' - imported by torch (conditional)
missing module named torch.trunc - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.tanh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.tan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.square - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sqrt - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sinh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sin - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.signbit - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sign - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.round - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.reciprocal - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.rad2deg - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.negative - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.logical_not - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log2 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log1p - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log10 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isnan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isinf - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isfinite - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.floor - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.expm1 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.exp2 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.exp - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.deg2rad - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.cosh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.cos - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.conj_physical - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.ceil - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.bitwise_not - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arctanh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arctan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arcsinh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arcsin - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arccosh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arccos - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.absolute - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.true_divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.subtract - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.remainder - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.pow - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.not_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.nextafter - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.multiply - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.minimum - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.maximum - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_xor - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_or - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_and - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logaddexp2 - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logaddexp - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.less_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.less - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.ldexp - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.lcm - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.hypot - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.heaviside - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.greater_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.greater - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.gcd - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmod - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmin - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmax - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.floor_divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.float_power - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.eq - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.copysign - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_xor - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_right_shift - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_or - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_left_shift - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_and - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.arctan2 - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.add - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.tensor - imported by torch (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), torch.utils.benchmark.utils.compare (top-level)
missing module named torch.float16 - imported by torch (delayed, conditional), torch._inductor.codegen.cpp_wrapper_cuda (delayed, conditional)
missing module named torch.bfloat16 - imported by torch (delayed, conditional), torch._inductor.codegen.cpp_wrapper_cuda (delayed, conditional)
missing module named torch._softmax_backward_data - imported by torch (delayed), transformers.pytorch_utils (delayed)
missing module named torch.randperm - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.Generator - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.default_generator - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.broadcast_shapes - imported by torch (top-level), torch._numpy._funcs_impl (top-level)
missing module named torch.ScriptObject - imported by torch (delayed), torch.export.graph_signature (delayed)
missing module named torch.norm_except_dim - imported by torch (top-level), torch.nn.utils.weight_norm (top-level)
missing module named torch._weight_norm - imported by torch (top-level), torch.nn.utils.weight_norm (top-level)
missing module named torch.Size - imported by torch (top-level), torch.types (top-level), transformers.models.nemotron.modeling_nemotron (top-level), torch.nn.modules.normalization (top-level)
missing module named torch.qscheme - imported by torch (top-level), torch.types (top-level)
missing module named torch.layout - imported by torch (top-level), torch.types (top-level)
missing module named torch.DispatchKey - imported by torch (top-level), torch.types (top-level)
missing module named torch.device - imported by torch (top-level), torch.types (top-level), torch.nn.modules.module (top-level), torch.cuda (top-level), torch._library.infer_schema (top-level), torch.distributed.nn.api.remote_module (top-level), torch._inductor.graph (top-level), transformers.models.blip.modeling_blip_text (top-level), torch.xpu (top-level), torch.cpu (top-level), torch.mtia (top-level)
missing module named cupy_backends - imported by scipy._lib.array_api_compat.common._helpers (delayed)
missing module named 'cupy.cuda' - imported by scipy._lib.array_api_compat.common._helpers (delayed)
missing module named 'numpy.linalg._linalg' - imported by scipy._lib.array_api_compat.numpy.linalg (delayed, optional)
missing module named 'numpy.lib.array_utils' - imported by joblib._memmapping_reducer (delayed, optional), scipy._lib.array_api_compat.common._linalg (conditional)
missing module named Cython - imported by scipy._lib._testutils (optional)
missing module named scipy._distributor_init_local - imported by scipy (optional), scipy._distributor_init (optional)
missing module named svgling - imported by nltk.tree.tree (delayed)
missing module named norm - imported by nltk.translate.gale_church (optional)
missing module named nltk.corpus.WordNetCorpusReader - imported by nltk.corpus (top-level), nltk.translate.meteor_score (top-level)
missing module named pycrfsuite - imported by nltk.tag.crf (optional)
missing module named 'sklearn.datasets' - imported by nltk.parse.transitionparser (optional)
missing module named 'bllipparser.RerankingParser' - imported by nltk.parse.bllip (optional)
missing module named bllipparser - imported by nltk.parse.bllip (optional)
missing module named 'sklearn.naive_bayes' - imported by nltk.classify.scikitlearn (conditional)
missing module named 'sklearn.linear_model' - imported by nltk.classify.scikitlearn (conditional)
missing module named 'sklearn.preprocessing' - imported by nltk.classify.scikitlearn (optional)
missing module named 'sklearn.feature_extraction' - imported by nltk.classify.scikitlearn (optional)
missing module named tornado - imported by joblib._dask (conditional, optional)
missing module named 'distributed.utils' - imported by joblib._dask (conditional, optional)
missing module named 'dask.utils' - imported by joblib._dask (conditional)
missing module named 'dask.sizeof' - imported by joblib._dask (conditional)
missing module named 'dask.distributed' - imported by joblib._dask (conditional)
missing module named dask - imported by joblib._dask (optional)
missing module named viztracer - imported by joblib.externals.loky.initializers (delayed, optional)
missing module named 'lz4.frame' - imported by joblib.compressor (optional)
missing module named nltk.induce_pcfg - imported by nltk (delayed), nltk.grammar (delayed)
missing module named nltk.Prover9 - imported by nltk (delayed), nltk.sem.glue (delayed)
missing module named nltk.word_tokenize - imported by nltk (delayed), nltk.classify.textcat (delayed)
missing module named nltk.FreqDist - imported by nltk (delayed), nltk.classify.textcat (delayed)
missing module named nltk.nonterminals - imported by nltk (delayed), nltk.parse.chart (delayed), nltk.grammar (delayed)
missing module named nltk.Tree - imported by nltk (delayed), nltk.tree.tree (delayed), nltk.chunk.regexp (delayed)
missing module named nltk.ProbabilisticTree - imported by nltk (delayed), nltk.tree.tree (delayed)
missing module named nltk.Production - imported by nltk (delayed), nltk.draw.cfg (delayed), nltk.parse.chart (delayed), nltk.grammar (delayed)
missing module named nltk.Nonterminal - imported by nltk (delayed), nltk.draw.cfg (delayed)
missing module named nltk.CFG - imported by nltk (delayed), nltk.draw.cfg (delayed), nltk.parse.chart (delayed), nltk.parse.recursivedescent (delayed), nltk.parse.shiftreduce (delayed), nltk.grammar (delayed)
missing module named numpypy - imported by nltk (optional)
missing module named pyimod02_importers - imported by D:\aaaaa\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed), D:\aaaaa\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named openpyxl.tests - imported by openpyxl.reader.excel (optional)
missing module named ttkbootstrap.dialogs.Dialog - imported by ttkbootstrap.dialogs (top-level), ttkbootstrap.dialogs.colorchooser (top-level)
missing module named 'qtpy.QtWidgets' - imported by pyperclip (delayed, optional)
missing module named 'Crypto.Util' - imported by PyPDF2._encryption (optional)
missing module named Crypto - imported by PyPDF2._encryption (optional)
