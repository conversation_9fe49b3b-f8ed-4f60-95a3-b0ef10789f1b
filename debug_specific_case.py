#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试具体的案例
"""

import re

def debug_specific_case():
    """调试具体的案例"""
    
    # 根据您截图中的具体情况
    skc_base = "48457830453"
    filename = "48457830453-4060.jpg"
    expected_width = 40
    expected_height = 60
    
    print("=== 调试具体案例 ===")
    print(f"SKC基础编号: {skc_base}")
    print(f"文件名: {filename}")
    print(f"期望尺寸: {expected_width}x{expected_height}cm")
    print()
    
    # 1. 检查图片分类逻辑
    print("1. 图片分类检查:")
    
    # 检查是否为多PC图片
    def is_multi_pc_image(filename, skc_base):
        if not filename or not skc_base:
            return False
        filename_lower = filename.lower()
        skc_lower = skc_base.lower()
        if not filename_lower.startswith(skc_lower):
            return False
        suffix = filename_lower[len(skc_lower):]
        multi_pc_pattern = r'^-[1-9]-\d{4}\.jpg$'
        return re.match(multi_pc_pattern, suffix) is not None
    
    is_multi_pc = is_multi_pc_image(filename, skc_base)
    print(f"  是否为多PC图片: {is_multi_pc}")
    
    # 检查是否为基础图片
    is_base_image = filename.lower() == f"{skc_base.lower()}.jpg"
    print(f"  是否为基础图片: {is_base_image}")
    
    # 检查是否为单PC尺寸格式
    if not is_multi_pc and not is_base_image:
        suffix = filename.lower()[len(skc_base.lower()):]
        print(f"  文件后缀: '{suffix}'")
        
        size_patterns = [
            r'^-\d{4}\.jpg$',           # -4060.jpg (4位数字)
            r'^-\d+x\d+\.jpg$',         # -40x60.jpg (带小写x)
            r'^-\d+X\d+\.jpg$',         # -40X60.jpg (带大写X)
            r'^_\d{4}\.jpg$',           # _4060.jpg (下划线+4位数字)
            r'^_\d+x\d+\.jpg$',         # _40x60.jpg (下划线+小写x)
            r'^_\d+X\d+\.jpg$',         # _40X60.jpg (下划线+大写X)
        ]
        
        for i, pattern in enumerate(size_patterns):
            match = re.match(pattern, suffix)
            print(f"  模式 {i+1} '{pattern}': {'✅ 匹配' if match else '❌ 不匹配'}")
        
        is_size_format = any(re.match(pattern, suffix) for pattern in size_patterns)
        print(f"  是否为尺寸格式: {is_size_format}")
        
        if is_size_format:
            print(f"  -> 归类为: 单PC多规格图片")
        else:
            print(f"  -> 归类为: 单PC图片（其他格式）")
    
    print()
    
    # 2. 检查尺寸匹配逻辑
    print("2. 尺寸匹配检查:")
    
    # 生成期望后缀
    expected_suffix = f"-{expected_width:02d}{expected_height:02d}"
    print(f"  生成的期望后缀: {expected_suffix}")
    
    # 生成所有可能的后缀
    possible_suffixes = [
        expected_suffix,  # -4060
        f"-{expected_width}x{expected_height}",  # -40x60
        f"-{expected_width}X{expected_height}",  # -40X60
        f"_{expected_width}x{expected_height}",  # _40x60
        f"_{expected_width}X{expected_height}",  # _40X60
    ]
    print(f"  所有可能的后缀: {possible_suffixes}")
    
    # 检查匹配
    matched = False
    matched_suffix = None
    for suffix in possible_suffixes:
        if suffix.lower() in filename.lower():
            matched = True
            matched_suffix = suffix
            print(f"  ✅ 匹配成功: 后缀 '{suffix}' 在文件名 '{filename}' 中")
            break
        else:
            print(f"  ❌ 后缀 '{suffix}' 不在文件名 '{filename}' 中")
    
    print()
    print("=== 结论 ===")
    if matched:
        print(f"✅ 应该匹配成功，返回图片: {filename}")
        print(f"   匹配的后缀: {matched_suffix}")
    else:
        print(f"❌ 匹配失败，会返回基础图片")
        print(f"   这表明代码中可能有其他问题")

if __name__ == "__main__":
    debug_specific_case()
