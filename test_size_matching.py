#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试尺寸匹配逻辑
"""

def test_size_matching():
    """测试各种尺寸格式的匹配"""
    
    # 模拟期望尺寸
    expected_width = 40
    expected_height = 60
    
    # 生成期望后缀（模拟get_size_suffix_from_dimensions函数）
    expected_suffix = f"-{expected_width:02d}{expected_height:02d}"  # -4060
    
    # 生成所有可能的后缀格式
    possible_suffixes = [
        expected_suffix,  # -4060 (来自get_size_suffix_from_dimensions的4位数字格式)
        f"-{expected_width}x{expected_height}",  # -40x60 (带小写x格式)
        f"-{expected_width}X{expected_height}",  # -40X60 (带大写X格式)
        f"_{expected_width}x{expected_height}",  # _40x60 (下划线+小写x格式)
        f"_{expected_width}X{expected_height}",  # _40X60 (下划线+大写X格式)
    ]
    
    # 测试文件名
    test_filenames = [
        "824839735563.jpg",           # 基础图片
        "824839735563-40x60.jpg",     # 带x格式
        "824839735563-40X60.jpg",     # 带大写X格式
        "824839735563-4060.jpg",      # 4位数字格式
        "824839735563_40x60.jpg",     # 下划线+x格式
        "824839735563_40X60.jpg",     # 下划线+大写X格式
        "824839735563-30x40.jpg",     # 不匹配的尺寸
        "824839735563-5070.jpg",      # 不匹配的4位数字
    ]
    
    print("=== 尺寸匹配测试 ===")
    print(f"期望尺寸: {expected_width}x{expected_height}cm")
    print(f"生成的后缀列表: {possible_suffixes}")
    print()
    
    for filename in test_filenames:
        matched = False
        matched_suffix = None
        
        for suffix in possible_suffixes:
            if suffix.lower() in filename.lower():
                matched = True
                matched_suffix = suffix
                break
        
        status = "✅ 匹配" if matched else "❌ 不匹配"
        suffix_info = f" (匹配后缀: {matched_suffix})" if matched else ""
        print(f"{status}: {filename}{suffix_info}")
    
    print()
    print("=== 预期结果 ===")
    print("应该匹配的文件:")
    print("- 824839735563-40x60.jpg (匹配后缀: -40x60)")
    print("- 824839735563-40X60.jpg (匹配后缀: -40X60)")
    print("- 824839735563-4060.jpg (匹配后缀: -4060)")
    print("- 824839735563_40x60.jpg (匹配后缀: _40x60)")
    print("- 824839735563_40X60.jpg (匹配后缀: _40X60)")
    print()
    print("不应该匹配的文件:")
    print("- 824839735563.jpg (基础图片)")
    print("- 824839735563-30x40.jpg (不同尺寸)")
    print("- 824839735563-5070.jpg (不同尺寸)")

if __name__ == "__main__":
    test_size_matching()
