#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面测试智能图片筛选功能
"""

import os
import re
import tempfile
import shutil

def extract_size_from_target_mapping(target_mapping):
    """从目标映射字符串中提取尺寸信息"""
    if not target_mapping:
        return None, None
    
    size_patterns = [
        r'(\d+)x(\d+)cm',  # 40x60cm
        r'(\d+)X(\d+)cm',  # 40X60cm
        r'(\d+)×(\d+)cm',  # 40×60cm
    ]
    
    for pattern in size_patterns:
        match = re.search(pattern, target_mapping)
        if match:
            width = int(match.group(1))
            height = int(match.group(2))
            return width, height
    
    return None, None

def get_size_suffix_from_dimensions(width, height):
    """根据尺寸生成对应的文件名后缀"""
    if width is None or height is None:
        return None
    return f"-{width:02d}{height:02d}"

def is_multi_pc_image(filename, skc_base):
    """判断图片是否为多PC格式"""
    if not filename or not skc_base:
        return False
    
    filename_lower = filename.lower()
    skc_lower = skc_base.lower()
    
    if not filename_lower.startswith(skc_lower):
        return False
    
    suffix = filename_lower[len(skc_lower):]
    multi_pc_pattern = r'^-\d+-\d+(?:-\d+)*(?:-?\d{4})?\.jpg$'
    
    return bool(re.match(multi_pc_pattern, suffix))

def find_images_by_skc_with_size_filter(source_folder, skc_base, target_mapping=None):
    """查找SKC相关的图片文件，支持单图片多规格的智能筛选"""
    if not os.path.isdir(source_folder):
        return []
    
    # 查找所有SKC相关的图片
    all_skc_images = []
    for filename in os.listdir(source_folder):
        if (filename.lower().startswith(skc_base.lower()) and 
            filename.lower().endswith('.jpg') and 
            os.path.isfile(os.path.join(source_folder, filename))):
            all_skc_images.append(filename)
    
    if not all_skc_images:
        return []
    
    # 分类图片
    base_images = []  # skc.jpg
    single_pc_size_images = []  # skc-4060.jpg, skc-5070.jpg
    multi_pc_images = []  # skc-1-2-4060.jpg
    
    for filename in all_skc_images:
        if is_multi_pc_image(filename, skc_base):
            multi_pc_images.append(filename)
        elif filename.lower() == f"{skc_base.lower()}.jpg":
            base_images.append(filename)
        else:
            suffix = filename.lower()[len(skc_base.lower()):]
            if re.match(r'^-\d{4}\.jpg$', suffix):
                single_pc_size_images.append(filename)
            else:
                single_pc_size_images.append(filename)
    
    # 如果有多PC图片，优先返回多PC图片
    if multi_pc_images:
        return [os.path.join(source_folder, f) for f in multi_pc_images]
    
    # 如果只有基础图片，直接返回
    if base_images and not single_pc_size_images:
        return [os.path.join(source_folder, f) for f in base_images]
    
    # 如果有单PC多规格图片，根据target_mapping筛选
    if single_pc_size_images:
        if not target_mapping:
            return [os.path.join(source_folder, f) for f in base_images + single_pc_size_images]
        
        # 从目标映射中提取期望的尺寸
        expected_width, expected_height = extract_size_from_target_mapping(target_mapping)
        
        if expected_width is None or expected_height is None:
            return [os.path.join(source_folder, f) for f in base_images + single_pc_size_images]
        
        # 生成期望的后缀
        expected_suffix = get_size_suffix_from_dimensions(expected_width, expected_height)
        
        if expected_suffix:
            # 查找匹配期望尺寸的图片
            matching_images = []
            for filename in single_pc_size_images:
                if expected_suffix.lower() in filename.lower():
                    matching_images.append(filename)
            
            if matching_images:
                return [os.path.join(source_folder, f) for f in matching_images]
            elif base_images:
                return [os.path.join(source_folder, f) for f in base_images]
            else:
                return [os.path.join(source_folder, f) for f in single_pc_size_images]
    
    # 默认返回所有找到的图片
    return [os.path.join(source_folder, f) for f in all_skc_images]

def run_test_case(test_name, skc, files, target_mapping, expected_result):
    """运行单个测试用例"""
    print(f"\n{'='*60}")
    print(f"测试用例: {test_name}")
    print(f"{'='*60}")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建测试文件
        for filename in files:
            with open(os.path.join(temp_dir, filename), 'w') as f:
                f.write("test")
        
        print(f"SKC: {skc}")
        print(f"可用图片: {files}")
        print(f"目标映射: {target_mapping}")
        
        # 执行筛选
        result = find_images_by_skc_with_size_filter(temp_dir, skc, target_mapping)
        result_files = [os.path.basename(p) for p in result]
        
        print(f"筛选结果: {result_files}")
        print(f"期望结果: {expected_result}")
        
        # 验证结果
        if set(result_files) == set(expected_result):
            print("✅ 测试通过")
            return True
        else:
            print("❌ 测试失败")
            return False

def comprehensive_test():
    """全面测试智能图片筛选功能"""
    print("🔍 开始全面测试智能图片筛选功能")
    
    skc = "12345678901"
    test_cases = [
        # 测试用例1：只有基础图片
        {
            "name": "只有基础图片",
            "files": [f"{skc}.jpg"],
            "target_mapping": "特级JIT-4-15-上/下午自送-1pc木框帆布画40x60cm-彭于晏-俞志敏组-(总X)-店铺号",
            "expected": [f"{skc}.jpg"]
        },
        
        # 测试用例2：基础图片 + 单PC多规格，匹配40x60
        {
            "name": "多规格匹配40x60",
            "files": [f"{skc}.jpg", f"{skc}-4060.jpg", f"{skc}-5070.jpg"],
            "target_mapping": "特级JIT-4-15-上/下午自送-1pc木框帆布画40x60cm-彭于晏-俞志敏组-(总X)-店铺号",
            "expected": [f"{skc}-4060.jpg"]
        },
        
        # 测试用例3：基础图片 + 单PC多规格，匹配50x70
        {
            "name": "多规格匹配50x70",
            "files": [f"{skc}.jpg", f"{skc}-4060.jpg", f"{skc}-5070.jpg"],
            "target_mapping": "特级JIT-4-15-上/下午自送-1pc木框帆布画50x70cm-彭于晏-俞志敏组-(总X)-店铺号",
            "expected": [f"{skc}-5070.jpg"]
        },
        
        # 测试用例4：基础图片 + 单PC多规格，匹配40x40
        {
            "name": "多规格匹配40x40",
            "files": [f"{skc}.jpg", f"{skc}-4040.jpg", f"{skc}-4060.jpg"],
            "target_mapping": "特级JIT-4-15-上/下午自送-1pc木框帆布画40x40cm-彭于晏-俞志敏组-(总X)-店铺号",
            "expected": [f"{skc}-4040.jpg"]
        },
        
        # 测试用例5：多PC图片优先
        {
            "name": "多PC图片优先",
            "files": [f"{skc}.jpg", f"{skc}-4060.jpg", f"{skc}-1-2-4060.jpg"],
            "target_mapping": "特级JIT-4-15-上/下午自送-1pc木框帆布画40x60cm-彭于晏-俞志敏组-(总X)-店铺号",
            "expected": [f"{skc}-1-2-4060.jpg"]
        },
        
        # 测试用例6：无匹配尺寸，回退到基础图片
        {
            "name": "无匹配尺寸回退",
            "files": [f"{skc}.jpg", f"{skc}-5070.jpg", f"{skc}-6080.jpg"],
            "target_mapping": "特级JIT-4-15-上/下午自送-1pc木框帆布画40x60cm-彭于晏-俞志敏组-(总X)-店铺号",
            "expected": [f"{skc}.jpg"]
        },
        
        # 测试用例7：无基础图片，无匹配尺寸
        {
            "name": "无基础图片无匹配",
            "files": [f"{skc}-5070.jpg", f"{skc}-6080.jpg"],
            "target_mapping": "特级JIT-4-15-上/下午自送-1pc木框帆布画40x60cm-彭于晏-俞志敏组-(总X)-店铺号",
            "expected": [f"{skc}-5070.jpg", f"{skc}-6080.jpg"]
        },
        
        # 测试用例8：无目标映射
        {
            "name": "无目标映射",
            "files": [f"{skc}.jpg", f"{skc}-4060.jpg", f"{skc}-5070.jpg"],
            "target_mapping": None,
            "expected": [f"{skc}.jpg", f"{skc}-4060.jpg", f"{skc}-5070.jpg"]
        },
        
        # 测试用例9：复杂多PC格式
        {
            "name": "复杂多PC格式",
            "files": [f"{skc}-1-2-3-4060.jpg", f"{skc}-1-2-4-5070.jpg"],
            "target_mapping": "特级JIT-4-15-上/下午自送-1pc木框帆布画40x60cm-彭于晏-俞志敏组-(总X)-店铺号",
            "expected": [f"{skc}-1-2-3-4060.jpg", f"{skc}-1-2-4-5070.jpg"]
        },
        
        # 测试用例10：30x40cm匹配
        {
            "name": "30x40cm匹配",
            "files": [f"{skc}.jpg", f"{skc}-3040.jpg", f"{skc}-4060.jpg"],
            "target_mapping": "特级JIT-4-15-上/下午自送-1pc木框帆布画30x40cm-彭于晏-俞志敏组-(总X)-店铺号",
            "expected": [f"{skc}-3040.jpg"]
        }
    ]
    
    # 运行所有测试用例
    passed = 0
    total = len(test_cases)
    
    for test_case in test_cases:
        if run_test_case(
            test_case["name"],
            skc,
            test_case["files"],
            test_case["target_mapping"],
            test_case["expected"]
        ):
            passed += 1
    
    # 输出测试总结
    print(f"\n{'='*60}")
    print(f"测试总结")
    print(f"{'='*60}")
    print(f"总测试用例: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试用例都通过了！")
    else:
        print("⚠️ 有测试用例失败，需要检查代码")

if __name__ == "__main__":
    comprehensive_test()
