#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正后的多PC格式测试
"""

import re

def is_multi_pc_image(filename, skc_base):
    """判断图片是否为多PC格式（修正版）"""
    if not filename or not skc_base:
        return False
    
    filename_lower = filename.lower()
    skc_lower = skc_base.lower()
    
    if not filename_lower.startswith(skc_lower):
        return False
    
    suffix = filename_lower[len(skc_lower):]
    
    # 多PC格式：包含PC序号和尺寸，如 -1-4060.jpg, -2-4060.jpg, -3-5070.jpg
    # 匹配模式：-数字-4位数字.jpg
    multi_pc_pattern = r'^-\d+-\d{4}\.jpg$'
    
    if re.match(multi_pc_pattern, suffix):
        print(f"🔍 [PC判断] {filename} 识别为多PC格式 (后缀: {suffix})")
        return True
    
    print(f"🔍 [PC判断] {filename} 识别为单PC格式 (后缀: {suffix})")
    return False

def test_corrected_pc_detection():
    """测试修正后的PC检测逻辑"""
    print("="*60)
    print("测试修正后的多PC格式识别")
    print("="*60)
    
    skc = "12345678901"
    
    # 测试用例
    test_cases = [
        # 基础图片
        (f"{skc}.jpg", False, "基础图片"),
        
        # 单PC多规格（只有尺寸后缀，没有PC序号）
        (f"{skc}-4060.jpg", False, "单PC-40x60尺寸"),
        (f"{skc}-5070.jpg", False, "单PC-50x70尺寸"),
        (f"{skc}-4040.jpg", False, "单PC-40x40尺寸"),
        (f"{skc}-3040.jpg", False, "单PC-30x40尺寸"),
        
        # 多PC格式（PC序号+尺寸）
        (f"{skc}-1-4060.jpg", True, "多PC-第1个PC-40x60尺寸"),
        (f"{skc}-2-4060.jpg", True, "多PC-第2个PC-40x60尺寸"),
        (f"{skc}-3-4060.jpg", True, "多PC-第3个PC-40x60尺寸"),
        (f"{skc}-1-5070.jpg", True, "多PC-第1个PC-50x70尺寸"),
        (f"{skc}-2-5070.jpg", True, "多PC-第2个PC-50x70尺寸"),
        
        # 单PC序号（没有尺寸）
        (f"{skc}-1.jpg", False, "单PC-第1个序号"),
        (f"{skc}-2.jpg", False, "单PC-第2个序号"),
        
        # 其他格式
        (f"{skc}-custom.jpg", False, "自定义后缀"),
        (f"{skc}-abc.jpg", False, "字母后缀"),
        
        # 错误格式（不应该匹配）
        (f"{skc}-1-60.jpg", False, "错误格式-2位数字"),
        (f"{skc}-1-40600.jpg", False, "错误格式-5位数字"),
        (f"{skc}-12-4060.jpg", False, "错误格式-2位PC序号"),
    ]
    
    print("\n测试结果:")
    print("-"*60)
    
    passed = 0
    total = len(test_cases)
    
    for filename, expected_multi_pc, description in test_cases:
        result = is_multi_pc_image(filename, skc)
        status = "✅ 通过" if result == expected_multi_pc else "❌ 失败"
        expected_text = "多PC" if expected_multi_pc else "单PC"
        actual_text = "多PC" if result else "单PC"
        
        print(f"{status} | {filename}")
        print(f"     描述: {description}")
        print(f"     期望: {expected_text}, 实际: {actual_text}")
        
        if result == expected_multi_pc:
            passed += 1
        
        print()
    
    print("="*60)
    print(f"测试总结: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试用例都通过了！")
    else:
        print("⚠️ 有测试用例失败，需要检查逻辑")

def test_real_scenarios():
    """测试真实场景"""
    print("\n" + "="*60)
    print("真实场景测试")
    print("="*60)
    
    skc = "12345678901"
    
    scenarios = [
        {
            "name": "场景1: 只有基础图片",
            "files": [f"{skc}.jpg"],
            "description": "最简单的情况，只有一张基础图片"
        },
        {
            "name": "场景2: 单PC多规格",
            "files": [f"{skc}.jpg", f"{skc}-4060.jpg", f"{skc}-5070.jpg"],
            "description": "有基础图片和多个尺寸的单PC图片"
        },
        {
            "name": "场景3: 多PC同尺寸",
            "files": [f"{skc}-1-4060.jpg", f"{skc}-2-4060.jpg", f"{skc}-3-4060.jpg"],
            "description": "多个PC，相同尺寸"
        },
        {
            "name": "场景4: 多PC多尺寸",
            "files": [f"{skc}-1-4060.jpg", f"{skc}-2-4060.jpg", f"{skc}-1-5070.jpg", f"{skc}-2-5070.jpg"],
            "description": "多个PC，多个尺寸"
        },
        {
            "name": "场景5: 混合格式",
            "files": [f"{skc}.jpg", f"{skc}-4060.jpg", f"{skc}-1-4060.jpg", f"{skc}-2-4060.jpg"],
            "description": "单PC和多PC混合"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n{scenario['name']}")
        print(f"描述: {scenario['description']}")
        print(f"文件: {scenario['files']}")
        
        # 分类文件
        base_images = []
        single_pc_images = []
        multi_pc_images = []
        
        for filename in scenario['files']:
            if is_multi_pc_image(filename, skc):
                multi_pc_images.append(filename)
            elif filename.lower() == f"{skc.lower()}.jpg":
                base_images.append(filename)
            else:
                single_pc_images.append(filename)
        
        print(f"分类结果:")
        print(f"  基础图片: {base_images}")
        print(f"  单PC图片: {single_pc_images}")
        print(f"  多PC图片: {multi_pc_images}")
        
        # 判断处理策略
        if multi_pc_images:
            print(f"  处理策略: 优先使用多PC图片 ({len(multi_pc_images)}个)")
        elif base_images and not single_pc_images:
            print(f"  处理策略: 使用基础图片 (按原方法)")
        elif single_pc_images:
            print(f"  处理策略: 根据属性集映射筛选单PC图片")
        else:
            print(f"  处理策略: 未知情况")

if __name__ == "__main__":
    test_corrected_pc_detection()
    test_real_scenarios()
