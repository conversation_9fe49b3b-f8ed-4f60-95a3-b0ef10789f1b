@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion
echo ========================================
echo 图片整理工具打包脚本
echo ========================================

:: 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

:: 检查主文件
if not exist "my_script.py" (
    echo 错误: 未找到 my_script.py 文件
    pause
    exit /b 1
)

:: 安装PyInstaller
echo 正在检查PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo 正在安装PyInstaller...
    python -m pip install pyinstaller
)

:: 清理旧文件
echo 清理旧的构建文件...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "*.spec" del /q "*.spec"

:: 执行打包
echo 开始打包...
python -m PyInstaller ^
    --onefile ^
    --windowed ^
    --name "图片整理工具" ^
    --clean ^
    --noconfirm ^
    --optimize 2 ^
    --strip ^
    --hidden-import tkinter ^
    --hidden-import tkinter.ttk ^
    --hidden-import tkinter.messagebox ^
    --hidden-import tkinter.filedialog ^
    --hidden-import pandas ^
    --hidden-import openpyxl ^
    --hidden-import PyPDF2 ^
    --hidden-import pdfplumber ^
    --hidden-import PIL ^
    --hidden-import PIL.Image ^
    --hidden-import pyperclip ^
    --hidden-import json ^
    --hidden-import logging ^
    --hidden-import threading ^
    --hidden-import queue ^
    --hidden-import concurrent.futures ^
    --exclude-module matplotlib ^
    --exclude-module numpy.random._examples ^
    --exclude-module numpy.tests ^
    --exclude-module pandas.tests ^
    --exclude-module PIL.ImageQt ^
    --exclude-module unittest ^
    --exclude-module pydoc ^
    --exclude-module doctest ^
    --exclude-module test ^
    --exclude-module distutils ^
    --exclude-module sqlite3 ^
    --exclude-module _tkinter ^
    --icon inco.ico ^
    my_script.py

:: 检查结果
if exist "dist\图片整理工具.exe" (
    echo ========================================
    echo 打包成功
    echo 文件位置: dist\图片整理工具.exe
    for %%I in ("dist\图片整理工具.exe") do (
        set /a size_mb=%%~zI/1024/1024
        echo 文件大小: %%~zI 字节 ^(约 !size_mb! MB^)
    )
    echo ========================================
) else (
    echo ========================================
    echo 打包失败
    echo ========================================
)

pause
