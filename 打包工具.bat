@echo off
chcp 65001 >nul
echo ========================================
echo ͼƬ�������ߴ���ű�
echo ========================================

:: ���Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ����: δ�ҵ�Python�����Ȱ�װPython
    pause
    exit /b 1
)

:: ������ļ�
if not exist "my_script.py" (
    echo ����: δ�ҵ� my_script.py �ļ�
    pause
    exit /b 1
)

:: ��װPyInstaller
echo ���ڼ��PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo ���ڰ�װPyInstaller...
    python -m pip install pyinstaller
)

:: �������ļ�
echo �����ɵĹ����ļ�...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "*.spec" del /q "*.spec"

:: ִ�д��
echo ��ʼ���...
python -m PyInstaller ^
    --onefile ^
    --windowed ^
    --name "ͼƬ��������" ^
    --clean ^
    --noconfirm ^
    --hidden-import tkinter ^
    --hidden-import tkinter.ttk ^
    --hidden-import tkinter.messagebox ^
    --hidden-import tkinter.filedialog ^
    --hidden-import pandas ^
    --hidden-import openpyxl ^
    --hidden-import PyPDF2 ^
    --hidden-import pdfplumber ^
    --hidden-import PIL ^
    --hidden-import PIL.Image ^
    --exclude-module matplotlib ^
    --exclude-module numpy.tests ^
    --exclude-module pandas.tests ^
    --exclude-module unittest ^
    --exclude-module test ^
    --icon inco.ico ^
    my_script.py

:: �����
if exist "dist\ͼƬ��������.exe" (
    echo ========================================
    echo ����ɹ���
    echo �ļ�λ��: dist\ͼƬ��������.exe
    for %%I in ("dist\ͼƬ��������.exe") do echo �ļ���С: %%~zI �ֽ�
    echo ========================================
) else (
    echo ========================================
    echo ���ʧ�ܣ�
    echo ========================================
)

pause
