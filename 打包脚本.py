#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拣货单处理工具打包脚本
优化版本 - 减少文件大小到最小
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import time

def print_step(step, message):
    """打印步骤信息"""
    print(f"\n{'='*50}")
    print(f"步骤 {step}: {message}")
    print('='*50)

def check_requirements():
    """检查打包环境"""
    print_step(1, "检查打包环境")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller已安装: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller未安装，正在安装...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
        print("✅ PyInstaller安装完成")
    
    # 检查必要文件
    required_files = ['my_script.py']
    optional_files = ['template_mapping.json', 'folder_config.json', 'app_config.json']

    for file in required_files:
        if os.path.exists(file):
            print(f"✅ 找到必需文件: {file}")
        else:
            print(f"❌ 缺少必需文件: {file}")
            return False

    for file in optional_files:
        if os.path.exists(file):
            print(f"✅ 找到配置文件: {file}")
        else:
            print(f"ℹ️ 可选文件不存在: {file}")
    
    return True

def clean_build():
    """清理构建目录"""
    print_step(2, "清理构建目录")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✅ 已清理目录: {dir_name}")
    
    # 清理spec文件
    for spec_file in Path('.').glob('*.spec'):
        spec_file.unlink()
        print(f"✅ 已清理文件: {spec_file}")

def create_icon():
    """创建或检查图标文件"""
    print_step(3, "检查图标文件")

    icon_files = ['inco.ico', 'app_icon.ico', 'icon.ico']
    for icon_file in icon_files:
        if os.path.exists(icon_file):
            print(f"✅ 找到图标文件: {icon_file}")
            return icon_file

    print("⚠️ 未找到图标文件，将使用默认图标")
    return None

def build_executable():
    """构建可执行文件"""
    print_step(4, "开始构建可执行文件")
    
    icon_file = create_icon()
    
    # 构建PyInstaller命令
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',                    # 单文件模式
        '--windowed',                   # 无控制台窗口
        '--name', '图片整理工具',        # 程序名称
        '--clean',                      # 清理缓存
        '--noconfirm',                  # 不确认覆盖
    ]

    # 添加配置文件（如果存在）
    config_files = ['template_mapping.json', 'folder_config.json', 'app_config.json']
    for config_file in config_files:
        if os.path.exists(config_file):
            cmd.extend(['--add-data', f'{config_file};.'])
            print(f"📋 添加配置文件: {config_file}")

    # 继续添加其他选项
    cmd.extend([
        # 排除不需要的模块以减少大小
        '--exclude-module', 'matplotlib',
        '--exclude-module', 'numpy.random._examples',
        '--exclude-module', 'numpy.tests',
        '--exclude-module', 'pandas.tests',
        '--exclude-module', 'PIL.ImageQt',
        '--exclude-module', 'unittest',
        '--exclude-module', 'pydoc',
        '--exclude-module', 'doctest',
        '--exclude-module', 'test',
        '--exclude-module', 'distutils',

        # 添加必要的隐藏导入
        '--hidden-import', 'tkinter',
        '--hidden-import', 'tkinter.ttk',
        '--hidden-import', 'tkinter.messagebox',
        '--hidden-import', 'tkinter.filedialog',
        '--hidden-import', 'pandas',
        '--hidden-import', 'openpyxl',
        '--hidden-import', 'PyPDF2',
        '--hidden-import', 'PIL',
        '--hidden-import', 'PIL.Image',
        '--hidden-import', 'pyperclip',
        '--hidden-import', 'ttkbootstrap',
        '--hidden-import', 'requests',
        '--hidden-import', 'json',
        '--hidden-import', 'logging',
        '--hidden-import', 'threading',
        '--hidden-import', 'queue',
        '--hidden-import', 'concurrent.futures',

        # 优化选项
        '--strip',                      # 去除调试信息
        '--optimize', '2',              # Python优化级别
    ])
    
    # 添加图标
    if icon_file:
        cmd.extend(['--icon', icon_file])
    
    # 添加主脚本
    cmd.append('my_script.py')
    
    print("🚀 执行构建命令...")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        # 执行构建 - 修复编码问题
        start_time = time.time()

        # 设置环境变量解决编码问题
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PYTHONUTF8'] = '1'

        # 不捕获输出，直接显示到控制台
        result = subprocess.run(cmd, check=True, env=env, encoding='utf-8', errors='ignore')
        end_time = time.time()

        print(f"✅ 构建成功！耗时: {end_time - start_time:.1f}秒")
        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        print("💡 尝试手动运行以下命令进行调试:")
        print(f"   {' '.join(cmd)}")
        return False

def check_result():
    """检查构建结果"""
    print_step(5, "检查构建结果")
    
    exe_path = Path('dist') / '图片整理工具.exe'
    
    if exe_path.exists():
        size_bytes = exe_path.stat().st_size
        size_mb = size_bytes / (1024 * 1024)
        
        print(f"✅ 可执行文件已生成: {exe_path}")
        print(f"📦 文件大小: {size_bytes:,} 字节 ({size_mb:.1f} MB)")
        
        # 如果文件较大，给出优化建议
        if size_mb > 100:
            print(f"⚠️ 文件较大 ({size_mb:.1f} MB)，建议:")
            print("   1. 检查是否有不必要的依赖")
            print("   2. 使用UPX压缩: upx --best dist/拣货单处理工具.exe")
        elif size_mb > 50:
            print(f"ℹ️ 文件大小适中 ({size_mb:.1f} MB)")
        else:
            print(f"✅ 文件大小良好 ({size_mb:.1f} MB)")
        
        return True
    else:
        print("❌ 未找到生成的可执行文件")
        return False

def try_upx_compression():
    """尝试UPX压缩"""
    print_step(6, "尝试UPX压缩")
    
    exe_path = Path('dist') / '图片整理工具.exe'
    
    if not exe_path.exists():
        print("❌ 可执行文件不存在，跳过压缩")
        return
    
    original_size = exe_path.stat().st_size
    
    try:
        # 尝试UPX压缩
        subprocess.run(['upx', '--best', str(exe_path)], check=True, capture_output=True)
        
        compressed_size = exe_path.stat().st_size
        compression_ratio = (1 - compressed_size / original_size) * 100
        
        print(f"✅ UPX压缩成功!")
        print(f"📦 原始大小: {original_size / (1024*1024):.1f} MB")
        print(f"📦 压缩后大小: {compressed_size / (1024*1024):.1f} MB")
        print(f"📈 压缩率: {compression_ratio:.1f}%")
        
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("ℹ️ UPX未安装或压缩失败，跳过压缩")
        print("💡 可以安装UPX来进一步减少文件大小: https://upx.github.io/")

def main():
    """主函数"""
    print("🔧 图片整理工具打包脚本")
    print("🎯 目标: 生成优化的单文件可执行程序")
    
    try:
        # 检查环境
        if not check_requirements():
            print("❌ 环境检查失败")
            return False
        
        # 清理构建
        clean_build()
        
        # 构建
        if not build_executable():
            print("❌ 构建失败")
            return False
        
        # 检查结果
        if not check_result():
            print("❌ 构建结果检查失败")
            return False
        
        # 尝试压缩
        try_upx_compression()
        
        print("\n🎉 打包完成!")
        print("📁 可执行文件位于: dist/图片整理工具.exe")
        print("💡 可以将此文件分发给用户使用")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断打包过程")
        return False
    except Exception as e:
        print(f"\n❌ 打包过程中出现错误: {e}")
        return False

if __name__ == '__main__':
    success = main()
    
    print("\n" + "="*50)
    if success:
        print("✅ 打包成功完成!")
    else:
        print("❌ 打包失败!")
    print("="*50)
    
    input("\n按回车键退出...")
