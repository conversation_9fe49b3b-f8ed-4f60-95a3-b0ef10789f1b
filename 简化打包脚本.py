#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版打包脚本 - 解决编码问题
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def main():
    """简化的打包流程"""
    print("开始打包图片整理工具...")

    # 1. 检查必要文件
    if not os.path.exists('my_script.py'):
        print("错误: 未找到 my_script.py 文件")
        return False

    # 2. 清理旧文件
    print("清理旧的构建文件...")
    for dir_name in ['build', 'dist', '__pycache__']:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已清理: {dir_name}")

    # 清理spec文件
    for spec_file in Path('.').glob('*.spec'):
        spec_file.unlink()
        print(f"已清理: {spec_file}")

    # 3. 检查PyInstaller
    try:
        import PyInstaller
        print(f"PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("正在安装PyInstaller...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
    
    # 4. 构建命令
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',                    # 单文件
        '--windowed',                   # 无控制台
        '--name', '图片整理工具',        # 程序名
        '--clean',                      # 清理缓存
        '--noconfirm',                  # 不确认
        '--optimize', '2',              # 优化级别
        '--strip',                      # 去除调试信息

        # 必要的导入
        '--hidden-import', 'tkinter',
        '--hidden-import', 'tkinter.ttk',
        '--hidden-import', 'tkinter.messagebox',
        '--hidden-import', 'tkinter.filedialog',
        '--hidden-import', 'pandas',
        '--hidden-import', 'openpyxl',
        '--hidden-import', 'PyPDF2',
        '--hidden-import', 'pdfplumber',
        '--hidden-import', 'PIL',
        '--hidden-import', 'PIL.Image',
        '--hidden-import', 'pyperclip',
        '--hidden-import', 'json',
        '--hidden-import', 'logging',
        '--hidden-import', 'threading',
        '--hidden-import', 'queue',
        '--hidden-import', 'concurrent.futures',

        # 排除不需要的模块
        '--exclude-module', 'matplotlib',
        '--exclude-module', 'numpy.random._examples',
        '--exclude-module', 'numpy.tests',
        '--exclude-module', 'pandas.tests',
        '--exclude-module', 'PIL.ImageQt',
        '--exclude-module', 'unittest',
        '--exclude-module', 'pydoc',
        '--exclude-module', 'doctest',
        '--exclude-module', 'test',
        '--exclude-module', 'distutils',
        '--exclude-module', 'sqlite3',

        'my_script.py'
    ]
    
    # 添加图标（如果存在）
    if os.path.exists('inco.ico'):
        cmd.extend(['--icon', 'inco.ico'])
        print("使用图标: inco.ico")

    # 添加配置文件（如果存在）
    config_files = ['app_config.json', 'template_mapping.json', 'folder_config.json']
    for config_file in config_files:
        if os.path.exists(config_file):
            cmd.extend(['--add-data', f'{config_file};.'])
            print(f"添加配置: {config_file}")

    # 5. 执行打包
    print("开始打包...")
    print("命令:", ' '.join(cmd))
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PYTHONUTF8'] = '1'
        
        # 执行命令
        process = subprocess.Popen(
            cmd, 
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            encoding='utf-8',
            errors='replace'
        )
        
        # 实时显示输出
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
        
        # 检查结果
        if process.returncode == 0:
            print("打包成功!")
        else:
            print(f"打包失败，退出码: {process.returncode}")
            return False

    except Exception as e:
        print(f"打包过程出错: {e}")
        return False

    # 6. 检查结果
    exe_path = Path('dist') / '图片整理工具.exe'
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print(f"打包完成!")
        print(f"文件位置: {exe_path}")
        print(f"文件大小: {size_mb:.1f} MB")
        return True
    else:
        print("未找到生成的exe文件")
        return False

if __name__ == '__main__':
    print("=" * 50)
    print("图片整理工具 - 简化打包脚本")
    print("=" * 50)

    success = main()

    print("\n" + "=" * 50)
    if success:
        print("打包成功完成!")
        print("可执行文件位于 dist/图片整理工具.exe")
    else:
        print("打包失败!")
        print("请检查错误信息并重试")
    print("=" * 50)

    input("\n按回车键退出...")
